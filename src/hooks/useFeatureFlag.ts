// Feature flag system for A/B testing and phased rollouts
import { useState, useEffect } from 'react';

export interface FeatureFlags {
  // Example feature flags
  darkModeToggle: boolean;
  advancedSearch: boolean;
  socialShare: boolean;
  newsletterModal: boolean;
  experimentalDesign: boolean;
  pwaDashboard: boolean;
}

// Default feature flag values
const defaultFlags: FeatureFlags = {
  darkModeToggle: true,
  advancedSearch: true,
  socialShare: true,
  newsletterModal: true,
  experimentalDesign: false,
  pwaDashboard: false,
};

// In a real implementation, this would fetch from a service like LaunchDarkly, Split.io, etc.
const fetchFeatureFlags = async (): Promise<Partial<FeatureFlags>> => {
  // Mock API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // In production, this would be an actual API call
  // return fetch('/api/feature-flags').then(res => res.json());
  
  // Mock overrides for testing
  return {
    // Example: Enable experimental features for specific conditions
    // experimentalDesign: Math.random() > 0.5, // 50% rollout
  };
};

export function useFeatureFlag<K extends keyof FeatureFlags>(
  flagName: K
): FeatureFlags[K] {
  const [flags, setFlags] = useState<FeatureFlags>(defaultFlags);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchFeatureFlags()
      .then(remoteFlags => {
        setFlags(prevFlags => ({ ...prevFlags, ...remoteFlags }));
      })
      .catch(error => {
        console.warn('Failed to fetch feature flags:', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Return default value while loading
  if (isLoading) {
    return defaultFlags[flagName];
  }

  return flags[flagName];
}

export function useFeatureFlags(): FeatureFlags & { isLoading: boolean } {
  const [flags, setFlags] = useState<FeatureFlags>(defaultFlags);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchFeatureFlags()
      .then(remoteFlags => {
        setFlags(prevFlags => ({ ...prevFlags, ...remoteFlags }));
      })
      .catch(error => {
        console.warn('Failed to fetch feature flags:', error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  return { ...flags, isLoading };
}