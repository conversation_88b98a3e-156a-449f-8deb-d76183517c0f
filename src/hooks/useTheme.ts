import { useState, useEffect } from 'react';
import { ThemeManager, Theme } from '../lib/theme';

export function useTheme() {
  const themeManager = ThemeManager.getInstance();
  const [theme, setTheme] = useState<Theme>(themeManager.getTheme());

  useEffect(() => {
    const unsubscribe = themeManager.subscribe((newTheme) => {
      setTheme(newTheme);
    });

    return unsubscribe;
  }, [themeManager]);

  const toggleTheme = () => {
    const effectiveTheme = themeManager.getEffectiveTheme();
    const newTheme = effectiveTheme === 'dark' ? 'light' : 'dark';
    themeManager.setTheme(newTheme);
  };

  return {
    theme,
    setTheme: themeManager.setTheme.bind(themeManager),
    toggleTheme,
    effectiveTheme: themeManager.getEffectiveTheme(),
  };
}