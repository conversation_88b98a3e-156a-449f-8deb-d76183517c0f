// Sitemap generation utilities
import { articles } from '../data/articles';
import { categories } from '../data/categories';

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export function generateSitemap(): SitemapUrl[] {
  const baseUrl = 'https://apextech.com';
  const urls: SitemapUrl[] = [];

  // Homepage
  urls.push({
    loc: baseUrl,
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 1.0
  });

  // Static pages
  const staticPages = [
    { path: '/about', priority: 0.8, changefreq: 'monthly' as const },
    { path: '/how-we-test', priority: 0.7, changefreq: 'monthly' as const },
    { path: '/reviews', priority: 0.9, changefreq: 'daily' as const },
    { path: '/guides', priority: 0.8, changefreq: 'weekly' as const },
    { path: '/news', priority: 0.7, changefreq: 'daily' as const }
  ];

  staticPages.forEach(page => {
    urls.push({
      loc: `${baseUrl}${page.path}`,
      lastmod: new Date().toISOString(),
      changefreq: page.changefreq,
      priority: page.priority
    });
  });

  // Category pages
  categories.forEach(category => {
    urls.push({
      loc: `${baseUrl}/category/${category.slug}`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: 0.8
    });
  });

  // Article pages
  articles.forEach(article => {
    urls.push({
      loc: `${baseUrl}/articles/${article.slug}`,
      lastmod: article.updated_date || article.published_date,
      changefreq: 'monthly',
      priority: 0.9
    });
  });

  return urls;
}

export function generateSitemapXML(): string {
  const urls = generateSitemap();
  
  const urlElements = urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlElements}
</urlset>`;
}

export function generateRobotsTxt(): string {
  const baseUrl = 'https://apextech.com';
  
  return `User-agent: *
Allow: /

# Sitemaps
Sitemap: ${baseUrl}/sitemap.xml

# Crawl-delay for respectful crawling
Crawl-delay: 1

# Block access to admin areas (if any)
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /static/

# Allow all other content
Allow: /articles/
Allow: /category/
Allow: /reviews/
Allow: /guides/
Allow: /news/
Allow: /about
Allow: /how-we-test
`;
}