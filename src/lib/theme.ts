// Apple-inspired theme management with light mode default
export type Theme = 'light' | 'dark' | 'system';

export class ThemeManager {
  private static instance: ThemeManager;
  private theme: Theme = 'light'; // Default to light mode (Apple-style)
  private listeners: Set<(theme: Theme) => void> = new Set();

  static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }

  constructor() {
    this.init();
  }

  private init() {
    // Load saved theme from localStorage, default to light
    const savedTheme = localStorage.getItem('apex-tech-theme') as Theme;
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      this.theme = savedTheme;
    } else {
      // Default to light mode for Apple-style experience
      this.theme = 'light';
      localStorage.setItem('apex-tech-theme', 'light');
    }

    // Apply initial theme
    this.applyTheme();

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', () => {
      if (this.theme === 'system') {
        this.applyTheme();
        this.notifyListeners();
      }
    });
  }

  private applyTheme() {
    const root = document.documentElement;
    
    // Remove any existing theme classes
    root.classList.remove('dark');
    
    if (this.theme === 'dark' || (this.theme === 'system' && this.getSystemTheme() === 'dark')) {
      root.classList.add('dark');
    }

    // Update meta theme-color for mobile browsers (Apple-style)
    const themeColorMeta = document.querySelector('meta[name="theme-color"]');
    if (themeColorMeta) {
      const isDark = root.classList.contains('dark');
      themeColorMeta.setAttribute('content', isDark ? '#0f172a' : '#ffffff');
    }

    // Smooth theme transitions
    root.style.setProperty('--theme-transition', 'background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), color 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1)');
  }

  private getSystemTheme(): 'light' | 'dark' {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  getTheme(): Theme {
    return this.theme;
  }

  setTheme(theme: Theme) {
    this.theme = theme;
    localStorage.setItem('apex-tech-theme', theme);
    this.applyTheme();
    this.notifyListeners();
  }

  subscribe(listener: (theme: Theme) => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.theme));
  }

  getEffectiveTheme(): 'light' | 'dark' {
    if (this.theme === 'system') {
      return this.getSystemTheme();
    }
    return this.theme;
  }

  // Prevent theme flicker on initial load
  static getInitialTheme(): Theme {
    if (typeof window === 'undefined') return 'light';
    
    const savedTheme = localStorage.getItem('apex-tech-theme') as Theme;
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      return savedTheme;
    }
    return 'light';
  }

  // Apply theme immediately to prevent flicker
  static applyInitialTheme() {
    if (typeof window === 'undefined') return;
    
    const theme = ThemeManager.getInitialTheme();
    const root = document.documentElement;
    
    if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }
}

// Initialize theme immediately to prevent flicker
if (typeof window !== 'undefined') {
  ThemeManager.applyInitialTheme();
}