// Internationalization configuration and utilities
// Centralized string management for future translation support

export const strings = {
  // Navigation
  nav: {
    home: 'Home',
    reviews: 'Reviews',
    guides: 'Guides',
    news: 'News',
    about: 'About',
    search: 'Search',
    searchPlaceholder: 'Search articles, reviews, and guides...',
    menu: 'Menu',
    close: 'Close',
  },
  
  // Common Actions
  actions: {
    readMore: 'Read More',
    viewAll: 'View All',
    subscribe: 'Subscribe',
    share: 'Share',
    copy: 'Copy',
    copied: 'Copied!',
    loading: 'Loading...',
    tryAgain: 'Try Again',
    goBack: 'Go Back',
    goHome: 'Go Home',
  },
  
  // Content
  content: {
    verdict: 'Our Verdict',
    pros: 'Pros',
    cons: 'Cons',
    rating: 'Rating',
    lastUpdated: 'Last Updated',
    readTime: 'min read',
    byAuthor: 'By',
    inCategory: 'in',
    relatedArticles: 'Related Articles',
    tableOfContents: 'Table of Contents',
    affiliateDisclosure: 'Affiliate Disclosure',
  },
  
  // Newsletter
  newsletter: {
    title: 'Stay Updated',
    subtitle: 'Get the latest tech reviews and insights delivered to your inbox.',
    emailPlaceholder: 'Enter your email address',
    subscribe: 'Subscribe',
    subscribing: 'Subscribing...',
    success: 'Thanks for subscribing!',
    error: 'Something went wrong. Please try again.',
    alreadySubscribed: 'You\'re already subscribed!',
  },
  
  // Error States
  errors: {
    pageNotFound: 'Page Not Found',
    pageNotFoundMessage: 'The page you\'re looking for doesn\'t exist or has been moved.',
    somethingWrong: 'Something went wrong',
    tryRefresh: 'Please try refreshing the page or check back later.',
    searchNoResults: 'No results found',
    searchNoResultsMessage: 'Try adjusting your search terms or browse our categories.',
  },
  
  // SEO and Meta
  meta: {
    defaultTitle: 'Apex Tech - Expert Tech Reviews & Analysis',
    defaultDescription: 'The definitive source for in-depth tech reviews, comparisons, and buying guides. Unbiased analysis of the latest gadgets and gear.',
    authorPrefix: 'By',
    publishedPrefix: 'Published',
    updatedPrefix: 'Updated',
  },
  
  // Accessibility
  a11y: {
    skipToContent: 'Skip to content',
    openMenu: 'Open navigation menu',
    closeMenu: 'Close navigation menu',
    toggleTheme: 'Toggle theme',
    searchButton: 'Search',
    shareButton: 'Share this article',
    scrollToTop: 'Scroll to top',
    externalLink: 'Opens in new tab',
    affiliateLink: 'Affiliate link',
  },
} as const;

// Type for string keys (for future TypeScript integration)
export type StringKey = keyof typeof strings;

// Utility function to get nested strings
export function getString(key: string): string {
  const keys = key.split('.');
  let current: any = strings;
  
  for (const k of keys) {
    if (current && typeof current === 'object' && k in current) {
      current = current[k];
    } else {
      console.warn(`Translation key not found: ${key}`);
      return key;
    }
  }
  
  return typeof current === 'string' ? current : key;
}

// Hook for using strings in components
export function useStrings() {
  return { strings, getString };
}