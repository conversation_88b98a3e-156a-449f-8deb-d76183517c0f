// Analytics integration for event tracking and user behavior analysis
export interface AnalyticsEvent {
  eventName: string;
  properties: Record<string, any>;
}

export const trackEvent = (eventName: string, properties: Record<string, any> = {}) => {
  // Add timestamp and page context
  const eventData: AnalyticsEvent = {
    eventName,
    properties: {
      ...properties,
      timestamp: new Date().toISOString(),
      page_url: window.location.href,
      page_title: document.title,
      user_agent: navigator.userAgent,
    },
  };

  // Log to console in development
  if (import.meta.env.DEV) {
    console.log('[Analytics]', eventData);
  }

  // TODO: Integrate with Vercel Analytics or Plausible
  // Example implementation:
  // if (window.va) {
  //   window.va('track', eventName, properties);
  // }
  
  // Example implementation for Plausible:
  // if (window.plausible) {
  //   window.plausible(eventName, { props: properties });
  // }
};

// Predefined event types for type safety
export const AnalyticsEvents = {
  // CTA Events
  CTA_CLICK: 'cta_click',
  AFFILIATE_LINK_CLICK: 'affiliate_link_click',
  
  // Navigation Events
  SEARCH_PERFORMED: 'search_performed',
  ARTICLE_VIEW: 'article_view',
  CATEGORY_VIEW: 'category_view',
  
  // Engagement Events
  NEWSLETTER_SIGNUP: 'newsletter_signup',
  NEWSLETTER_SIGNUP_SUCCESS: 'newsletter_signup_success',
  NEWSLETTER_SIGNUP_ERROR: 'newsletter_signup_error',
  
  // Error Events
  ERROR_BOUNDARY_TRIGGERED: 'error_boundary_triggered',
  SEARCH_ERROR: 'search_error',
  
  // Performance Events
  PAGE_LOAD_TIME: 'page_load_time',
} as const;

export type AnalyticsEventType = typeof AnalyticsEvents[keyof typeof AnalyticsEvents];