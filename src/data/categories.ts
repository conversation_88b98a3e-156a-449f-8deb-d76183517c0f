import { Category } from './schema';
import categoriesData from './categories.json';

export const categories: Category[] = categoriesData.categories.map(cat => ({
  id: cat.id,
  name: cat.name,
  slug: cat.slug,
  description: cat.description,
  image: cat.image,
  article_count: cat.articleCount,
}));

// Helper functions
export function getCategoryBySlug(slug: string): Category | undefined {
  return categories.find(category => category.slug === slug);
}

export function getMainCategories(): Category[] {
  return categories.filter(category => !category.parent_id);
}

export function getSubCategories(parentId: string): Category[] {
  return categories.filter(category => category.parent_id === parentId);
}

export function getCategoryHierarchy(categoryId: string): Category[] {
  const category = categories.find(c => c.id === categoryId);
  if (!category) return [];

  const hierarchy = [category];
  
  if (category.parent_id) {
    const parent = categories.find(c => c.id === category.parent_id);
    if (parent) {
      hierarchy.unshift(parent);
    }
  }

  return hierarchy;
}

export function getFeaturedCategories(): Category[] {
  return categoriesData.categories
    .filter(cat => cat.featured)
    .map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      description: cat.description,
      image: cat.image,
      article_count: cat.articleCount,
    }));
}