// CMS Mock Schema - Type definitions for content structure
export interface Author {
  id: string;
  name: string;
  avatar?: string;
  bio?: string;
  social?: {
    twitter?: string;
    linkedin?: string;
    website?: string;
  };
}

export interface ContentMeta {
  status: 'draft' | 'review' | 'published';
  version: number;
  authors: Author[];
  lastModified: string;
  last_fact_check_date: string;
  review_cycle_number: number;
  editor_notes: string[];
}

export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  og_image?: string;
  canonical_url?: string;
}

export interface ReviewData {
  product_name: string;
  brand: string;
  model?: string;
  price?: {
    msrp: number;
    current?: number;
    currency: string;
  };
  rating: {
    overall: number; // 1-5 scale
    categories: {
      design: number;
      performance: number;
      value: number;
      features: number;
    };
  };
  pros: string[];
  cons: string[];
  verdict: string;
  affiliate_links?: {
    amazon?: string;
    best_buy?: string;
    direct?: string;
  };
}

export interface Article {
  id: string;
  slug: string;
  title: string;
  excerpt: string;
  content: string;
  type: 'review' | 'guide' | 'news' | 'comparison';
  category: string;
  tags: string[];
  featured_image?: string;
  images?: string[];
  reading_time: number; // minutes
  published_date: string;
  updated_date?: string;
  
  // Meta information
  meta: ContentMeta;
  seo: SEOData;
  
  // Review-specific data (optional)
  review_data?: ReviewData;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  image?: string;
  parent_id?: string;
  article_count: number;
}

export interface NewsletterSubscriber {
  email: string;
  subscribed_date: string;
  preferences?: {
    weekly_digest: boolean;
    breaking_news: boolean;
    product_launches: boolean;
  };
}