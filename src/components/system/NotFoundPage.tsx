import React from 'react';
import { motion } from 'framer-motion';
import { Search, Home, ArrowRight, FileText, Compass, TrendingUp, AlertTriangle } from 'lucide-react';
import { getFeaturedArticles } from '../../data/articles';
import { getMainCategories } from '../../data/categories';
import { AppleCard } from '../ui/AppleCard';
import { SEO } from '../seo/SEO';
import { trackEvent, AnalyticsEvents } from '../../lib/analytics';

export function NotFoundPage() {
  const suggestedArticles = getFeaturedArticles(3);
  const popularCategories = getMainCategories().slice(0, 4);

  const handleSearchSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const query = formData.get('search') as string;
    if (query.trim()) {
      trackEvent(AnalyticsEvents.SEARCH_PERFORMED, {
        query,
        location: 'NotFoundPage',
      });
      window.location.href = `/?search=${encodeURIComponent(query)}`;
    }
  };

  const handleCTAClick = (action: string, destination: string) => {
    trackEvent(AnalyticsEvents.CTA_CLICK, {
      location: 'NotFoundPage',
      component_id: action.toLowerCase().replace(/\s+/g, '-'),
      destination,
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <SEO
        title="Page Not Found - Apex Tech"
        description="The page you're looking for doesn't exist. Browse our latest tech reviews and guides instead."
        keywords={['404', 'page not found', 'tech reviews', 'apex tech']}
        noindex={true}
      />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-12"
          >
            {/* 404 Illustration */}
            <div className="w-32 h-32 bg-gradient-to-br from-error-100 to-error-200 dark:from-error-900/20 dark:to-error-800/20 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-apple">
              <AlertTriangle className="w-16 h-16 text-error-600 dark:text-error-400" />
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 tracking-tight">
              404 - Page Not Found
            </h1>
            
            <p className="text-xl text-neutral-600 dark:text-neutral-400 mb-8 max-w-2xl mx-auto leading-relaxed">
              Looks like this page took a detour into the digital void. Don't worry—we'll help you find what you're looking for.
            </p>

            {/* Quick Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <a
                href="/"
                onClick={() => handleCTAClick('Go Home', '/')}
                className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-xl transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
              >
                <Home className="w-5 h-5 mr-2" />
                Go Home
              </a>
              
              <a
                href="/categories"
                onClick={() => handleCTAClick('Browse Categories', '/categories')}
                className="inline-flex items-center px-6 py-3 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 text-neutral-900 dark:text-neutral-100 font-semibold rounded-xl border border-neutral-200 dark:border-neutral-700 transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
              >
                <FileText className="w-5 h-5 mr-2" />
                Browse Categories
              </a>
            </div>
          </motion.div>

          {/* Search Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mb-16"
          >
            <div className="bg-neutral-50 dark:bg-neutral-800 rounded-3xl p-8 max-w-2xl mx-auto">
              <div className="flex items-center justify-center mb-6">
                <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center">
                  <Search className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                </div>
              </div>
              
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
                Search for Content
              </h2>
              
              <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                Try searching for reviews, guides, or specific products you're interested in.
              </p>
              
              <form onSubmit={handleSearchSubmit}>
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                  <input
                    type="text"
                    name="search"
                    placeholder="Search articles, reviews, and guides..."
                    className="w-full pl-12 pr-4 py-4 text-lg bg-white dark:bg-neutral-700 border border-neutral-200 dark:border-neutral-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                    autoFocus
                  />
                </div>
              </form>
            </div>
          </motion.div>

          {/* Popular Categories */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-16"
          >
            <div className="flex items-center justify-center mb-8">
              <Compass className="w-6 h-6 text-primary-600 dark:text-primary-400 mr-3" />
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
                Explore Categories
              </h2>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {popularCategories.map((category, index) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                >
                  <a
                    href={`/category/${category.slug}`}
                    onClick={() => handleCTAClick('Category Click', category.slug)}
                    className="block p-6 bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 hover:border-primary-300 dark:hover:border-primary-600 transition-all hover:scale-105 shadow-apple hover:shadow-apple-hover group"
                  >
                    <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center mb-4 group-hover:bg-primary-200 dark:group-hover:bg-primary-900/30 transition-colors">
                      <div className="w-6 h-6 bg-primary-600 dark:bg-primary-400 rounded" />
                    </div>
                    <h3 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                      {category.name}
                    </h3>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-3 line-clamp-2">
                      {category.description}
                    </p>
                    <div className="text-xs text-primary-600 dark:text-primary-400 font-medium">
                      {category.article_count} articles
                    </div>
                  </a>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Popular Articles */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <div className="flex items-center justify-center mb-8">
              <TrendingUp className="w-6 h-6 text-primary-600 dark:text-primary-400 mr-3" />
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
                Popular Articles
              </h2>
            </div>
            
            <div className="grid md:grid-cols-3 gap-6">
              {suggestedArticles.map((article, index) => (
                <motion.div
                  key={article.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                >
                  <AppleCard
                    title={article.title}
                    description={article.excerpt}
                    image={article.featured_image}
                    imageAlt={article.title}
                    href={`/articles/${article.slug}`}
                    onClick={() => handleCTAClick('Article Click', article.slug)}
                    badge={{
                      text: article.type,
                      variant: 'primary'
                    }}
                    rating={article.review_data?.rating.overall}
                    date={formatDate(article.published_date)}
                    readTime={`${article.reading_time} min`}
                    size="small"
                    variant="minimal"
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Help Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-16"
          >
            <div className="bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 rounded-3xl p-8">
              <h2 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
                Still Can't Find What You're Looking For?
              </h2>
              <p className="text-neutral-600 dark:text-neutral-400 mb-6 max-w-2xl mx-auto">
                Our team is here to help. Reach out if you have questions or suggestions for content you'd like to see.
              </p>
              <a
                href="mailto:<EMAIL>"
                onClick={() => handleCTAClick('Contact Us', 'mailto:<EMAIL>')}
                className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-xl transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
              >
                Contact Us
                <ArrowRight className="ml-2 w-4 h-4" />
              </a>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}