import React from 'react';
import { motion } from 'framer-motion';

interface LoadingSkeletonProps {
  variant?: 'text' | 'card' | 'article' | 'hero' | 'list';
  lines?: number;
  className?: string;
}

export function LoadingSkeleton({ 
  variant = 'text', 
  lines = 3, 
  className = '' 
}: LoadingSkeletonProps) {
  const skeletonVariants = {
    initial: { opacity: 0.6 },
    animate: { opacity: 1 },
  };

  const SkeletonLine = ({ width = 'w-full' }: { width?: string }) => (
    <motion.div
      variants={skeletonVariants}
      initial="initial"
      animate="animate"
      transition={{
        duration: 1.2,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut"
      }}
      className={`h-4 bg-neutral-200 dark:bg-neutral-700 rounded ${width}`}
    />
  );

  const SkeletonCard = () => (
    <div className="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-6 space-y-4">
      <motion.div
        variants={skeletonVariants}
        initial="initial"
        animate="animate"
        transition={{
          duration: 1.2,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
        className="h-48 bg-neutral-200 dark:bg-neutral-700 rounded-lg"
      />
      <div className="space-y-3">
        <SkeletonLine width="w-3/4" />
        <SkeletonLine width="w-full" />
        <SkeletonLine width="w-1/2" />
      </div>
    </div>
  );

  const SkeletonArticle = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <SkeletonLine width="w-3/4" />
        <SkeletonLine width="w-1/2" />
      </div>
      <motion.div
        variants={skeletonVariants}
        initial="initial"
        animate="animate"
        transition={{
          duration: 1.2,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
        className="h-64 bg-neutral-200 dark:bg-neutral-700 rounded-lg"
      />
      <div className="space-y-3">
        {Array.from({ length: 8 }).map((_, i) => (
          <SkeletonLine 
            key={i} 
            width={i % 3 === 0 ? 'w-5/6' : i % 3 === 1 ? 'w-full' : 'w-3/4'} 
          />
        ))}
      </div>
    </div>
  );

  const SkeletonHero = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <SkeletonLine width="w-full" />
        <SkeletonLine width="w-4/5" />
        <SkeletonLine width="w-2/3" />
      </div>
      <motion.div
        variants={skeletonVariants}
        initial="initial"
        animate="animate"
        transition={{
          duration: 1.2,
          repeat: Infinity,
          repeatType: "reverse",
          ease: "easeInOut"
        }}
        className="h-96 bg-neutral-200 dark:bg-neutral-700 rounded-lg"
      />
    </div>
  );

  const SkeletonList = () => (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="flex space-x-4 p-4 bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700">
          <motion.div
            variants={skeletonVariants}
            initial="initial"
            animate="animate"
            transition={{
              duration: 1.2,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut",
              delay: i * 0.1
            }}
            className="w-20 h-20 bg-neutral-200 dark:bg-neutral-700 rounded-lg flex-shrink-0"
          />
          <div className="flex-1 space-y-2">
            <SkeletonLine width="w-3/4" />
            <SkeletonLine width="w-1/2" />
            <SkeletonLine width="w-1/3" />
          </div>
        </div>
      ))}
    </div>
  );

  const renderSkeleton = () => {
    switch (variant) {
      case 'card':
        return <SkeletonCard />;
      case 'article':
        return <SkeletonArticle />;
      case 'hero':
        return <SkeletonHero />;
      case 'list':
        return <SkeletonList />;
      default:
        return (
          <div className="space-y-3">
            {Array.from({ length: lines }).map((_, i) => (
              <SkeletonLine 
                key={i} 
                width={i === lines - 1 ? 'w-3/4' : 'w-full'} 
              />
            ))}
          </div>
        );
    }
  };

  return (
    <div className={`animate-pulse ${className}`} role="status" aria-label="Loading content">
      {renderSkeleton()}
    </div>
  );
}