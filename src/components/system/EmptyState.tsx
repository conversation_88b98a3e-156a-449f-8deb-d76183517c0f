import React from 'react';
import { motion } from 'framer-motion';
import { Search, FileText, Wifi, AlertCircle } from 'lucide-react';

interface EmptyStateProps {
  variant?: 'search' | 'articles' | 'offline' | 'error' | 'custom';
  title?: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: React.ReactNode;
}

export function EmptyState({
  variant = 'search',
  title,
  description,
  action,
  icon,
}: EmptyStateProps) {
  const getDefaultContent = () => {
    switch (variant) {
      case 'search':
        return {
          icon: <Search className="w-12 h-12 text-neutral-400" />,
          title: 'No results found',
          description: 'Try adjusting your search terms or browse our categories.',
        };
      case 'articles':
        return {
          icon: <FileText className="w-12 h-12 text-neutral-400" />,
          title: 'No articles yet',
          description: 'Check back soon for new content and reviews.',
        };
      case 'offline':
        return {
          icon: <Wifi className="w-12 h-12 text-neutral-400" />,
          title: 'You\'re offline',
          description: 'Please check your internet connection and try again.',
        };
      case 'error':
        return {
          icon: <AlertCircle className="w-12 h-12 text-error-400" />,
          title: 'Something went wrong',
          description: 'We encountered an error while loading this content.',
        };
      default:
        return {
          icon: <FileText className="w-12 h-12 text-neutral-400" />,
          title: 'Nothing here',
          description: 'This section is empty.',
        };
    }
  };

  const defaultContent = getDefaultContent();
  const finalIcon = icon || defaultContent.icon;
  const finalTitle = title || defaultContent.title;
  const finalDescription = description || defaultContent.description;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="flex flex-col items-center justify-center text-center p-12"
    >
      <motion.div
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.1, duration: 0.3 }}
        className="mb-6"
      >
        {finalIcon}
      </motion.div>
      
      <motion.h3
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-2"
      >
        {finalTitle}
      </motion.h3>
      
      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="text-neutral-600 dark:text-neutral-400 max-w-sm mb-6"
      >
        {finalDescription}
      </motion.p>
      
      {action && (
        <motion.button
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          onClick={action.onClick}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
        >
          {action.label}
        </motion.button>
      )}
    </motion.div>
  );
}