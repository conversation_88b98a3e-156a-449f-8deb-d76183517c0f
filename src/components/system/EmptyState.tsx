import React from 'react';
import { motion } from 'framer-motion';
import { Search, FileText, Wifi, AlertCircle } from 'lucide-react';

interface EmptyStateProps {
  variant?: 'search' | 'articles' | 'offline' | 'error' | 'custom';
  title?: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: React.ReactNode;
}

export function EmptyState({
  variant = 'search',
  title,
  description,
  action,
  icon,
}: EmptyStateProps) {
  const getDefaultContent = () => {
    switch (variant) {
      case 'search':
        return {
          icon: <Search className="w-12 h-12 text-neutral-400" />,
          title: 'No results found',
          description: 'We couldn\'t find any content matching your search. Try using different keywords, checking your spelling, or browse our categories to discover new content.',
        };
      case 'articles':
        return {
          icon: <FileText className="w-12 h-12 text-neutral-400" />,
          title: 'No articles yet',
          description: 'This section is currently empty, but we\'re working on adding new content. Check back soon for the latest tech reviews and analysis.',
        };
      case 'offline':
        return {
          icon: <Wifi className="w-12 h-12 text-neutral-400" />,
          title: 'You\'re offline',
          description: 'It looks like you\'re not connected to the internet. Please check your connection and try refreshing the page.',
        };
      case 'error':
        return {
          icon: <AlertCircle className="w-12 h-12 text-error-400" />,
          title: 'Something went wrong',
          description: 'We encountered an unexpected error while loading this content. Please try refreshing the page or contact support if the problem persists.',
        };
      default:
        return {
          icon: <FileText className="w-12 h-12 text-neutral-400" />,
          title: 'Nothing here yet',
          description: 'This section is currently empty. Content will appear here once it becomes available.',
        };
    }
  };

  const defaultContent = getDefaultContent();
  const finalIcon = icon || defaultContent.icon;
  const finalTitle = title || defaultContent.title;
  const finalDescription = description || defaultContent.description;

  const getHelpfulSuggestions = () => {
    switch (variant) {
      case 'search':
        return [
          'Try using broader search terms',
          'Check your spelling',
          'Browse our categories instead',
          'Use fewer keywords'
        ];
      case 'articles':
        return [
          'Subscribe to our newsletter for updates',
          'Follow us on social media',
          'Check out our other categories',
          'Suggest topics you\'d like to see covered'
        ];
      case 'offline':
        return [
          'Check your WiFi connection',
          'Try refreshing the page',
          'Restart your router if needed',
          'Contact your internet provider'
        ];
      case 'error':
        return [
          'Refresh the page',
          'Clear your browser cache',
          'Try again in a few minutes',
          'Contact support if the issue persists'
        ];
      default:
        return [];
    }
  };

  const suggestions = getHelpfulSuggestions();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="flex flex-col items-center justify-center text-center p-12"
    >
      <motion.div
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.1, duration: 0.3 }}
        className="mb-6"
      >
        {finalIcon}
      </motion.div>
      
      <motion.h3
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-2"
      >
        {finalTitle}
      </motion.h3>
      
      <motion.p
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="text-neutral-600 dark:text-neutral-400 max-w-md mb-6 leading-relaxed"
      >
        {finalDescription}
      </motion.p>

      {suggestions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.3 }}
          className="mb-6 w-full max-w-sm"
        >
          <h4 className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-3">
            Try these suggestions:
          </h4>
          <ul className="text-sm text-neutral-600 dark:text-neutral-400 space-y-1 text-left">
            {suggestions.map((suggestion, index) => (
              <motion.li
                key={index}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 + index * 0.1, duration: 0.3 }}
                className="flex items-start"
              >
                <span className="text-primary-500 mr-2 flex-shrink-0">•</span>
                <span>{suggestion}</span>
              </motion.li>
            ))}
          </ul>
        </motion.div>
      )}

      {action && (
        <motion.button
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          onClick={action.onClick}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
        >
          {action.label}
        </motion.button>
      )}
    </motion.div>
  );
}