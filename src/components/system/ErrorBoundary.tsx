import React, { Component, ErrorInfo, ReactNode } from 'react';
import { trackEvent, AnalyticsEvents } from '../../lib/analytics';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });
    
    // Track error for analytics
    trackEvent(AnalyticsEvents.ERROR_BOUNDARY_TRIGGERED, {
      error_message: error.message,
      error_stack: error.stack,
      component_stack: errorInfo.componentStack,
    });

    // Log to console in development
    if (import.meta.env.DEV) {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-neutral-50 dark:bg-neutral-900 flex items-center justify-center p-6">
          <div className="max-w-md w-full text-center">
            <div className="bg-white dark:bg-neutral-800 rounded-xl shadow-lg p-8 border border-neutral-200 dark:border-neutral-700">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 bg-error-100 dark:bg-error-900/20 rounded-full flex items-center justify-center">
                  <AlertTriangle className="w-8 h-8 text-error-600 dark:text-error-400" />
                </div>
              </div>
              
              <h1 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-2">
                Something went wrong
              </h1>
              
              <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                We apologize for the inconvenience. Please try refreshing the page or return to the homepage.
              </p>

              {import.meta.env.DEV && this.state.error && (
                <details className="text-left mb-6 p-4 bg-neutral-100 dark:bg-neutral-900 rounded-lg text-sm">
                  <summary className="cursor-pointer font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Error Details (Development)
                  </summary>
                  <pre className="text-xs text-error-600 dark:text-error-400 overflow-auto">
                    {this.state.error.message}
                    {this.state.error.stack}
                  </pre>
                </details>
              )}

              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={this.handleRetry}
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </button>
                
                <a
                  href="/"
                  className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-neutral-300 dark:border-neutral-600 text-sm font-medium rounded-lg text-neutral-700 dark:text-neutral-300 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Go Home
                </a>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}