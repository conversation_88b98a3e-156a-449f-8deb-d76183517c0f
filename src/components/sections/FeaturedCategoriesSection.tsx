import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { CategoryCard } from '../ui/CategoryCard';
import categoriesData from '../../data/categories.json';

export function FeaturedCategoriesSection() {
  const featuredCategories = categoriesData.categories.filter(category => category.featured);

  const handleCategoryClick = (categorySlug: string, categoryName: string) => {
    // Track category click if analytics is available
    console.log(`Category clicked: ${categoryName} (${categorySlug})`);
  };

  return (
    <section id="categories" className="py-20 lg:py-32 bg-neutral-50/50 dark:bg-neutral-800/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-16">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 tracking-tight">
              Featured Categories
            </h2>
            <p className="text-xl text-neutral-600 dark:text-neutral-400">
              Explore our most popular tech categories
            </p>
          </motion.div>
          
          <motion.a
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            href="/categories"
            className="hidden sm:inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
          >
            View All
            <ArrowRight className="ml-2 w-4 h-4" />
          </motion.a>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredCategories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <CategoryCard
                title={category.name}
                description={category.description}
                image={category.image}
                href={`/category/${category.slug}`}
                articleCount={category.articleCount}
                onClick={() => handleCategoryClick(category.slug, category.name)}
                variant="featured"
              />
            </motion.div>
          ))}
        </div>

        <div className="text-center mt-12 sm:hidden">
          <a
            href="/categories"
            className="inline-flex items-center px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
          >
            View All Categories
            <ArrowRight className="ml-2 w-4 h-4" />
          </a>
        </div>
      </div>
    </section>
  );
}