import React from 'react';
import { motion } from 'framer-motion';
import { Award, Shield, Users, Zap, CheckCircle, Star } from 'lucide-react';
import { animations } from '../../utils/animations';

export function TrustSection() {
  const trustIndicators = [
    {
      icon: Award,
      title: 'Expert Analysis',
      description: 'Our team of experienced reviewers conducts thorough testing and analysis of every product.',
      stats: '500+ Reviews',
      color: 'from-primary-500 to-primary-600'
    },
    {
      icon: Shield,
      title: 'Unbiased Testing',
      description: 'We maintain editorial independence and never let sponsorships influence our opinions.',
      stats: '100% Independent',
      color: 'from-success-500 to-success-600'
    },
    {
      icon: Users,
      title: 'Community Trusted',
      description: 'Trusted by millions of tech enthusiasts who rely on our honest recommendations.',
      stats: '2M+ Readers',
      color: 'from-accent-500 to-accent-600'
    },
    {
      icon: Zap,
      title: 'Latest Technology',
      description: 'We stay ahead of tech trends to bring you insights on the newest innovations.',
      stats: 'Weekly Updates',
      color: 'from-secondary-500 to-secondary-600'
    }
  ];

  const certifications = [
    { label: 'Reviewed by Experts', icon: CheckCircle },
    { label: 'Unbiased Testing Process', icon: Shield },
    { label: 'Transparent Affiliate Disclosure', icon: Star },
    { label: 'Real-World Performance Focus', icon: Zap }
  ];

  return (
    <section className="py-20 lg:py-32 bg-neutral-50/50 dark:bg-neutral-800/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight">
            Why Trust Apex Tech?
          </h2>
          <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto leading-relaxed">
            We're committed to providing honest, thorough, and unbiased tech reviews that help you make confident decisions.
          </p>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          variants={animations.staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
        >
          {trustIndicators.map((indicator, index) => (
            <motion.div
              key={index}
              variants={animations.staggerItem}
              whileHover={{ y: -8, scale: 1.02 }}
              className="text-center group"
            >
              <div className={`w-20 h-20 bg-gradient-to-br ${indicator.color} rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-apple group-hover:shadow-apple-hover transition-all duration-300`}>
                <indicator.icon className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-100 mb-3">
                {indicator.title}
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed mb-4">
                {indicator.description}
              </p>
              <div className="inline-flex px-3 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium">
                {indicator.stats}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Certifications */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="bg-white dark:bg-neutral-800 rounded-3xl p-8 lg:p-12 shadow-apple dark:shadow-apple-dark border border-neutral-200/50 dark:border-neutral-700/50"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
              Our Commitment to Quality
            </h3>
            <p className="text-neutral-600 dark:text-neutral-400 max-w-2xl mx-auto">
              Every review follows our rigorous testing methodology and editorial standards.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {certifications.map((cert, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
                className="flex items-center p-4 bg-neutral-50 dark:bg-neutral-700 rounded-2xl border border-neutral-200 dark:border-neutral-600 hover:border-primary-300 dark:hover:border-primary-600 transition-all cursor-pointer"
              >
                <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center mr-3 flex-shrink-0">
                  <cert.icon className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                </div>
                <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                  {cert.label}
                </span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}