import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Clock, TrendingUp, ArrowRight, Loader2 } from 'lucide-react';
import Fuse from 'fuse.js';
import { articles } from '../../data/articles';
import { categories } from '../../data/categories';
import { animations } from '../../utils/animations';
import { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';
import { RippleEffect } from '../ui/RippleEffect';

interface SearchResult {
  id: string;
  title: string;
  type: 'article' | 'category';
  excerpt?: string;
  url: string;
  image?: string;
  category?: string;
}

interface EnhancedSearchOverlayProps {
  isOpen: boolean;
  onClose: () => void;
}

export function EnhancedSearchOverlay({ isOpen, onClose }: EnhancedSearchOverlayProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);

  // Convert data to search format
  const searchData: SearchResult[] = [
    ...articles.map(article => ({
      id: article.id,
      title: article.title,
      type: 'article' as const,
      excerpt: article.excerpt,
      url: `/articles/${article.slug}`,
      image: article.featured_image,
      category: article.category,
    })),
    ...categories.map(category => ({
      id: category.id,
      title: category.name,
      type: 'category' as const,
      excerpt: category.description,
      url: `/category/${category.slug}`,
      image: category.image,
    })),
  ];

  // Initialize Fuse.js for fuzzy search
  const fuse = new Fuse(searchData, {
    keys: [
      { name: 'title', weight: 0.7 },
      { name: 'excerpt', weight: 0.3 },
      { name: 'category', weight: 0.2 },
    ],
    threshold: 0.3,
    includeScore: true,
    minMatchCharLength: 2,
  });

  // Enhanced keyboard navigation
  useKeyboardNavigation({
    onEscape: onClose,
    onArrowDown: () => setSelectedIndex(prev => {
      const maxIndex = results.length - 1;
      if (maxIndex < 0) return -1;
      return prev < maxIndex ? prev + 1 : 0; // Wrap to beginning
    }),
    onArrowUp: () => setSelectedIndex(prev => {
      const maxIndex = results.length - 1;
      if (maxIndex < 0) return -1;
      return prev > 0 ? prev - 1 : maxIndex; // Wrap to end
    }),
    onEnter: () => {
      if (selectedIndex >= 0 && results[selectedIndex]) {
        handleResultClick(results[selectedIndex]);
      } else if (query.trim() && results.length === 0) {
        // If no results but there's a query, perform a general search
        window.location.href = `/?search=${encodeURIComponent(query)}`;
        onClose();
      }
    },
    enabled: isOpen
  });

  // Focus input when overlay opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Prevent body scroll when overlay is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [isOpen]);

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('apex-tech-recent-searches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch (error) {
        console.warn('Failed to parse recent searches:', error);
      }
    }
  }, []);

  // Perform search with debouncing
  useEffect(() => {
    if (query.length === 0) {
      setResults([]);
      setIsSearching(false);
      setSelectedIndex(-1);
      return;
    }

    if (query.length < 2) {
      setResults([]);
      setIsSearching(false);
      setSelectedIndex(-1);
      return;
    }

    setIsSearching(true);
    
    const timer = setTimeout(() => {
      const searchResults = fuse.search(query);
      const formattedResults = searchResults
        .slice(0, 8)
        .map(result => result.item);
      
      setResults(formattedResults);
      setIsSearching(false);
      setSelectedIndex(-1);
    }, 300);

    return () => clearTimeout(timer);
  }, [query, fuse]);

  const handleResultClick = (result: SearchResult) => {
    // Save to recent searches
    const newRecentSearches = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
    setRecentSearches(newRecentSearches);
    localStorage.setItem('apex-tech-recent-searches', JSON.stringify(newRecentSearches));
    
    // Navigate to the result
    window.location.href = result.url;
    onClose();
  };

  const handleRecentSearchClick = (recentQuery: string) => {
    setQuery(recentQuery);
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('apex-tech-recent-searches');
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'article':
        return 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400';
      case 'category':
        return 'bg-secondary-100 text-secondary-800 dark:bg-secondary-900/20 dark:text-secondary-400';
      default:
        return 'bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-400';
    }
  };

  const trendingSearches = ['iPhone 15', 'Gaming Monitors', 'Noise Canceling', 'MacBook Pro', 'Steam Deck'];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          variants={animations.searchOverlay}
          initial="initial"
          animate="animate"
          exit="exit"
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
          onClick={onClose}
          role="dialog"
          aria-modal="true"
          aria-label="Search"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -20 }}
            transition={{ duration: 0.2 }}
            className="absolute top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl mx-auto px-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="bg-white dark:bg-neutral-800 rounded-2xl shadow-apple dark:shadow-apple-dark border border-neutral-200 dark:border-neutral-700 overflow-hidden">
              {/* Search Input */}
              <div className="p-4 border-b border-neutral-200 dark:border-neutral-700">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                  <input
                    ref={inputRef}
                    type="text"
                    placeholder="Search articles and categories..."
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    className="w-full pl-12 pr-12 py-4 text-lg bg-transparent border-0 focus:ring-0 focus:outline-none text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400"
                    autoComplete="off"
                    spellCheck="false"
                  />
                  
                  {isSearching && (
                    <div className="absolute right-12 top-1/2 transform -translate-y-1/2">
                      <Loader2 className="w-5 h-5 text-primary-500 animate-spin" />
                    </div>
                  )}
                  
                  <RippleEffect
                    onClick={onClose}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 p-1 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300 transition-colors rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700"
                  >
                    <X className="w-5 h-5" />
                  </RippleEffect>
                </div>
              </div>

              {/* Content */}
              <div className="max-h-96 overflow-auto">
                {query.length === 0 ? (
                  /* Empty State */
                  <div className="p-6 space-y-6">
                    {/* Recent Searches */}
                    {recentSearches.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="flex items-center text-sm font-medium text-neutral-700 dark:text-neutral-300">
                            <Clock className="w-4 h-4 mr-2" />
                            Recent Searches
                          </h3>
                          <button
                            onClick={clearRecentSearches}
                            className="text-xs text-neutral-500 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors"
                          >
                            Clear
                          </button>
                        </div>
                        <div className="space-y-1">
                          {recentSearches.map((recentQuery, index) => (
                            <motion.button
                              key={index}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: 0.2 + index * 0.05 }}
                              onClick={() => handleRecentSearchClick(recentQuery)}
                              className="block w-full text-left px-3 py-2 text-neutral-900 dark:text-neutral-100 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-lg transition-colors"
                            >
                              {recentQuery}
                            </motion.button>
                          ))}
                        </div>
                      </motion.div>
                    )}

                    {/* Trending Searches */}
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <h3 className="flex items-center text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-3">
                        <TrendingUp className="w-4 h-4 mr-2" />
                        Trending
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {trendingSearches.map((trending, index) => (
                          <motion.button
                            key={index}
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.4 + index * 0.05 }}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => handleRecentSearchClick(trending)}
                            className="px-3 py-1.5 bg-neutral-100 dark:bg-neutral-700 hover:bg-neutral-200 dark:hover:bg-neutral-600 text-neutral-700 dark:text-neutral-300 rounded-full text-sm transition-colors"
                          >
                            {trending}
                          </motion.button>
                        ))}
                      </div>
                    </motion.div>
                  </div>
                ) : (
                  /* Search Results */
                  <div className="p-4">
                    {results.length > 0 ? (
                      <div className="space-y-2">
                        <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-3">
                          {results.length} result{results.length !== 1 ? 's' : ''} for "{query}"
                        </p>
                        {results.map((result, index) => (
                          <motion.button
                            key={result.id}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05 }}
                            onClick={() => handleResultClick(result)}
                            className={`w-full text-left p-3 rounded-lg transition-colors ${
                              selectedIndex === index
                                ? 'bg-primary-50 dark:bg-primary-900/20'
                                : 'hover:bg-neutral-50 dark:hover:bg-neutral-700'
                            }`}
                          >
                            <div className="flex items-start space-x-3">
                              {result.image && (
                                <img
                                  src={result.image}
                                  alt=""
                                  className="w-12 h-12 object-cover rounded-lg flex-shrink-0"
                                />
                              )}
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className={`inline-flex px-2 py-0.5 text-xs font-medium rounded-full ${getTypeColor(result.type)}`}>
                                    {result.type}
                                  </span>
                                  {result.category && (
                                    <span className="text-xs text-neutral-500 dark:text-neutral-400">
                                      {result.category}
                                    </span>
                                  )}
                                </div>
                                <h4 className="font-medium text-neutral-900 dark:text-neutral-100 line-clamp-1 mb-1">
                                  {result.title}
                                </h4>
                                {result.excerpt && (
                                  <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-2">
                                    {result.excerpt}
                                  </p>
                                )}
                              </div>
                              <ArrowRight className="w-4 h-4 text-neutral-400 flex-shrink-0 mt-1" />
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    ) : (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="text-center py-8 space-y-4"
                      >
                        <Search className="w-12 h-12 text-neutral-400 mx-auto mb-4" />
                        <div>
                          <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-2">
                            No results found for "{query}"
                          </h3>
                          <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                            We couldn't find any articles or categories matching your search.
                          </p>
                        </div>

                        <div className="space-y-3">
                          <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                            Try these suggestions:
                          </p>
                          <div className="text-sm text-neutral-600 dark:text-neutral-400 space-y-1">
                            <p>• Check your spelling and try again</p>
                            <p>• Use fewer or different keywords</p>
                            <p>• Search for broader terms (e.g., "laptop" instead of "MacBook Pro M3")</p>
                            <p>• Browse our <button
                                onClick={() => { window.location.href = '/categories'; onClose(); }}
                                className="text-primary-600 dark:text-primary-400 hover:underline"
                              >categories</button> to discover content</p>
                          </div>
                        </div>

                        <div className="pt-4">
                          <button
                            onClick={() => { window.location.href = `/?search=${encodeURIComponent(query)}`; onClose(); }}
                            className="inline-flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors text-sm font-medium"
                          >
                            Search all content
                            <ArrowRight className="w-4 h-4 ml-2" />
                          </button>
                        </div>
                      </motion.div>
                    )}
                  </div>
                )}
              </div>

              {/* Footer hint */}
              <div className="px-4 py-3 bg-neutral-50 dark:bg-neutral-900 border-t border-neutral-200 dark:border-neutral-700">
                <div className="flex items-center justify-between text-xs text-neutral-500 dark:text-neutral-400">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <kbd className="px-1.5 py-0.5 bg-neutral-200 dark:bg-neutral-700 rounded text-xs">↑↓</kbd>
                      <span className="ml-1">Navigate</span>
                    </span>
                    <span className="flex items-center">
                      <kbd className="px-1.5 py-0.5 bg-neutral-200 dark:bg-neutral-700 rounded text-xs">↵</kbd>
                      <span className="ml-1">Select</span>
                    </span>
                  </div>
                  <span className="flex items-center">
                    <kbd className="px-1.5 py-0.5 bg-neutral-200 dark:bg-neutral-700 rounded text-xs">ESC</kbd>
                    <span className="ml-1">Close</span>
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}