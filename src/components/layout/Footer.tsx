import React from 'react';
import { Mail, Twitter, Youtube, Github } from 'lucide-react';

export function Footer() {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    content: [
      { label: 'Reviews', href: '/reviews' },
      { label: 'Guides', href: '/guides' },
      { label: 'News', href: '/news' },
      { label: 'Categories', href: '/categories' },
    ],
    company: [
      { label: 'About', href: '/about' },
      { label: 'Editorial Policy', href: '/editorial-policy' },
      { label: 'Contact', href: '/contact' },
      { label: 'Careers', href: '/careers' },
    ],
    legal: [
      { label: 'Privacy Policy', href: '/privacy' },
      { label: 'Terms of Service', href: '/terms' },
      { label: 'Cookie Policy', href: '/cookies' },
      { label: 'Affiliate Disclosure', href: '/affiliate-disclosure' },
    ],
  };

  const socialLinks = [
    { icon: Twitter, href: 'https://twitter.com/apextech', label: 'Twitter' },
    { icon: Youtube, href: 'https://youtube.com/apextech', label: 'YouTube' },
    { icon: Github, href: 'https://github.com/apextech', label: 'GitHub' },
    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' },
  ];

  return (
    <footer className="bg-neutral-900 text-neutral-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">A</span>
              </div>
              <span className="font-bold text-xl text-white">Apex Tech</span>
            </div>
            <p className="text-neutral-400 mb-6 max-w-md">
              The definitive source for in-depth tech reviews, comparisons, and buying guides. 
              Unbiased analysis of the latest gadgets and gear.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map(({ icon: Icon, href, label }) => (
                <a
                  key={label}
                  href={href}
                  className="w-10 h-10 bg-neutral-800 hover:bg-neutral-700 rounded-lg flex items-center justify-center transition-colors group"
                  aria-label={label}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Icon className="w-5 h-5 text-neutral-400 group-hover:text-white transition-colors" />
                </a>
              ))}
            </div>
          </div>

          {/* Links */}
          <div>
            <h3 className="font-semibold text-white mb-4">Content</h3>
            <ul className="space-y-3">
              {footerLinks.content.map(({ label, href }) => (
                <li key={label}>
                  <a
                    href={href}
                    className="text-neutral-400 hover:text-white transition-colors"
                  >
                    {label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-white mb-4">Company</h3>
            <ul className="space-y-3">
              {footerLinks.company.map(({ label, href }) => (
                <li key={label}>
                  <a
                    href={href}
                    className="text-neutral-400 hover:text-white transition-colors"
                  >
                    {label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-white mb-4">Legal</h3>
            <ul className="space-y-3">
              {footerLinks.legal.map(({ label, href }) => (
                <li key={label}>
                  <a
                    href={href}
                    className="text-neutral-400 hover:text-white transition-colors"
                  >
                    {label}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="border-t border-neutral-800 mt-12 pt-8 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-neutral-400 text-sm">
            &copy; {currentYear} Apex Tech. All rights reserved.
          </p>
          <p className="text-neutral-500 text-xs mt-2 sm:mt-0">
            Built with care for the tech community
          </p>
        </div>
      </div>
    </footer>
  );
}