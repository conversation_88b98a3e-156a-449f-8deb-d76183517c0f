import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Search, Menu } from 'lucide-react';
import { ThemeToggle } from '../ui/ThemeToggle';

interface HeaderProps {
  onSearchOpen: () => void;
  onMobileMenuOpen: () => void;
}

export function Header({ onSearchOpen, onMobileMenuOpen }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navigationItems = [
    { href: '/', label: 'Home', isActive: window.location.pathname === '/' },
    { href: '/categories', label: 'Categories', isActive: window.location.pathname.startsWith('/category') },
    { href: '/about', label: 'About', isActive: window.location.pathname === '/about' },
    { href: '/how-we-test', label: 'How We Test', isActive: window.location.pathname === '/how-we-test' },
  ];

  return (
    <motion.header
      initial={false}
      animate={{
        backgroundColor: isScrolled 
          ? 'rgba(255, 255, 255, 0.95)' 
          : 'rgba(255, 255, 255, 0)',
        backdropFilter: isScrolled ? 'blur(20px)' : 'blur(0px)',
        borderBottomColor: isScrolled 
          ? 'rgba(0, 0, 0, 0.1)'
          : 'rgba(0, 0, 0, 0)',
      }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="sticky top-0 z-40 border-b border-transparent dark:border-neutral-800"
      role="banner"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <a
              href="/"
              className="flex items-center space-x-3 group focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 rounded-xl p-1 -m-1"
              aria-label="Apex Tech Home"
            >
              <motion.div 
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-apple"
              >
                <span className="text-white font-bold text-lg">A</span>
              </motion.div>
              <span className="font-semibold text-xl text-neutral-900 dark:text-neutral-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                Apex Tech
              </span>
            </a>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:block" aria-label="Main navigation">
            <div className="ml-10 flex items-baseline space-x-1">
              {navigationItems.map((item) => (
                <a
                  key={item.href}
                  href={item.href}
                  className={`px-4 py-2 text-sm font-medium transition-all rounded-xl hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50 focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 ${
                    item.isActive
                      ? 'text-primary-600 dark:text-primary-400 bg-primary-50/50 dark:bg-primary-900/20'
                      : 'text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-neutral-100'
                  }`}
                >
                  {item.label}
                </a>
              ))}
            </div>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-2">
            {/* Search Button */}
            <button
              onClick={onSearchOpen}
              className="p-2.5 text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100 hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50 rounded-xl transition-all focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 group relative"
              aria-label="Search (Press / or Cmd+K)"
              title="Search (Press / or Cmd+K)"
            >
              <Search className="w-5 h-5" />
              
              {/* Keyboard shortcut hint */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                <div className="bg-neutral-900 dark:bg-neutral-100 text-white dark:text-neutral-900 text-xs px-2 py-1 rounded flex items-center space-x-1 whitespace-nowrap">
                  <span>/</span>
                </div>
              </div>
            </button>

            {/* Theme Toggle */}
            <ThemeToggle variant="dropdown" />

            {/* Mobile Menu Button */}
            <button
              onClick={onMobileMenuOpen}
              className="md:hidden p-2.5 text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100 hover:bg-neutral-100/50 dark:hover:bg-neutral-800/50 rounded-xl transition-all focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
              aria-label="Open mobile menu"
            >
              <Menu className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </motion.header>
  );
}