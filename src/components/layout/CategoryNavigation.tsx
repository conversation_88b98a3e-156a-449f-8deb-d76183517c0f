import React, { useRef, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { getMainCategories } from '../../data/categories';
import { trackEvent, AnalyticsEvents } from '../../lib/analytics';

export function CategoryNavigation() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  // Get only main categories (no duplicates)
  const categories = getMainCategories();

  const checkScrollButtons = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const handleResize = () => checkScrollButtons();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = 200;
      const newScrollLeft = scrollRef.current.scrollLeft + (direction === 'right' ? scrollAmount : -scrollAmount);
      scrollRef.current.scrollTo({ left: newScrollLeft, behavior: 'smooth' });
    }
  };

  const handleCategoryClick = (categorySlug: string, categoryName: string) => {
    setActiveCategory(categorySlug);
    trackEvent(AnalyticsEvents.CATEGORY_VIEW, {
      category_slug: categorySlug,
      category_name: categoryName,
      location: 'CategoryNavigation',
    });
  };

  return (
    <section className="bg-white/95 dark:bg-neutral-900/95 backdrop-blur-xl border-b border-neutral-200/50 dark:border-neutral-800/50 sticky top-16 z-30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative py-4">
          {/* Left scroll button */}
          {canScrollLeft && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-white dark:bg-neutral-800 shadow-apple dark:shadow-apple-dark rounded-full flex items-center justify-center text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100 transition-colors"
              aria-label="Scroll categories left"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
          )}

          {/* Categories container */}
          <div
            ref={scrollRef}
            onScroll={checkScrollButtons}
            className="flex items-center space-x-2 overflow-x-auto scrollbar-hide scroll-smooth px-8 lg:px-0"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {categories.map((category, index) => (
              <motion.a
                key={category.id}
                href={`/category/${category.slug}`}
                onClick={() => handleCategoryClick(category.slug, category.name)}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className={`group relative flex-shrink-0 px-6 py-3 text-sm font-medium rounded-full transition-all duration-200 hover:scale-105 ${
                  activeCategory === category.slug
                    ? 'bg-primary-600 text-white shadow-apple'
                    : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-100'
                }`}
              >
                <span className="relative z-10">{category.name}</span>
                
                {/* Hover effect */}
                <motion.div
                  className="absolute inset-0 bg-primary-50 dark:bg-primary-900/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  layoutId="categoryHover"
                />
              </motion.a>
            ))}
          </div>

          {/* Right scroll button */}
          {canScrollRight && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 bg-white dark:bg-neutral-800 shadow-apple dark:shadow-apple-dark rounded-full flex items-center justify-center text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100 transition-colors"
              aria-label="Scroll categories right"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </section>
  );
}