import React from 'react';
import { motion } from 'framer-motion';
import { animations } from '../../utils/animations';

interface AnimatedCardProps {
  children: React.ReactNode;
  className?: string;
  href?: string;
  onClick?: () => void;
  variant?: 'default' | 'hover' | 'press';
  delay?: number;
}

export function AnimatedCard({ 
  children, 
  className = '', 
  href, 
  onClick, 
  variant = 'default',
  delay = 0 
}: AnimatedCardProps) {
  const cardVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.5, 
        delay,
        ease: [0.4, 0, 0.2, 1] 
      }
    }
  };

  const hoverVariants = variant === 'hover' ? {
    whileHover: animations.cardHover,
    whileTap: animations.cardTap
  } : {};

  const CardContent = () => (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      {...hoverVariants}
      className={`${className} ${variant === 'hover' ? 'cursor-pointer' : ''}`}
    >
      {children}
    </motion.div>
  );

  if (href) {
    return (
      <a href={href} onClick={onClick} className="block focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 rounded-2xl">
        <CardContent />
      </a>
    );
  }

  if (onClick) {
    return (
      <button onClick={onClick} className="block w-full text-left focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 rounded-2xl">
        <CardContent />
      </button>
    );
  }

  return <CardContent />;
}