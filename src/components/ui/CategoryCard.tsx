import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { Image } from '../media/Image';

interface CategoryCardProps {
  title: string;
  description: string;
  image?: string;
  href: string;
  articleCount?: number;
  onClick?: () => void;
  className?: string;
  variant?: 'default' | 'featured' | 'compact';
}

export function CategoryCard({
  title,
  description,
  image,
  href,
  articleCount,
  onClick,
  className = '',
  variant = 'default',
}: CategoryCardProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case 'featured':
        return 'p-8';
      case 'compact':
        return 'p-4';
      default:
        return 'p-6';
    }
  };

  const getImageAspect = () => {
    switch (variant) {
      case 'featured':
        return 'aspect-[16/10]';
      case 'compact':
        return 'aspect-square';
      default:
        return 'aspect-video';
    }
  };

  const CardContent = () => (
    <motion.div
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={`group bg-white dark:bg-neutral-800 rounded-2xl shadow-apple dark:shadow-apple-dark border border-neutral-200/50 dark:border-neutral-700/50 overflow-hidden hover:shadow-apple-hover dark:hover:shadow-apple-dark-hover transition-all duration-300 ${className}`}
    >
      {/* Image */}
      {image && (
        <div className={`relative ${getImageAspect()} overflow-hidden`}>
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="w-full h-full"
          >
            <Image
              src={image}
              alt={title}
              className="w-full h-full object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </motion.div>
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          {/* Article Count Badge */}
          {articleCount && (
            <div className="absolute top-4 right-4 bg-white/90 dark:bg-neutral-800/90 backdrop-blur-sm rounded-lg px-3 py-1.5 shadow-sm">
              <span className="text-sm font-semibold text-neutral-900 dark:text-neutral-100">
                {articleCount} articles
              </span>
            </div>
          )}
        </div>
      )}
      
      {/* Content */}
      <div className={getVariantClasses()}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className={`font-bold text-neutral-900 dark:text-neutral-100 mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors line-clamp-2 ${
              variant === 'featured' ? 'text-2xl' : variant === 'compact' ? 'text-lg' : 'text-xl'
            }`}>
              {title}
            </h3>
            
            <p className={`text-neutral-600 dark:text-neutral-400 leading-relaxed mb-4 ${
              variant === 'compact' ? 'line-clamp-2 text-sm' : 'line-clamp-3'
            }`}>
              {description}
            </p>
            
            {/* Article count for non-image variants */}
            {!image && articleCount && (
              <div className="text-sm text-primary-600 dark:text-primary-400 font-medium mb-4">
                {articleCount} articles
              </div>
            )}
          </div>
          
          {/* Arrow */}
          <motion.div
            whileHover={{ x: 4 }}
            transition={{ duration: 0.2 }}
            className="ml-4"
          >
            <ArrowRight className="w-5 h-5 text-neutral-400 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors" />
          </motion.div>
        </div>
      </div>
    </motion.div>
  );

  if (href) {
    return (
      <a 
        href={href} 
        onClick={onClick} 
        className="block focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 rounded-2xl"
      >
        <CardContent />
      </a>
    );
  }

  if (onClick) {
    return (
      <button 
        onClick={onClick} 
        className="block w-full text-left focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 rounded-2xl"
      >
        <CardContent />
      </button>
    );
  }

  return <CardContent />;
}