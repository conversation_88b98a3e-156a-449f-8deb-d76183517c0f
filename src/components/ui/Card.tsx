import React from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { animations } from '../../utils/animations';

interface CardProps extends Omit<HTMLMotionProps<'div'>, 'children'> {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  interactive?: boolean;
  className?: string;
}

export function Card({
  children,
  variant = 'default',
  size = 'md',
  hover = false,
  interactive = false,
  className = '',
  ...props
}: CardProps) {
  const baseClasses = 'rounded-xl transition-all duration-300 ease-out';
  
  const variantClasses = {
    default: 'bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800',
    elevated: 'bg-white dark:bg-neutral-900 shadow-apple dark:shadow-apple-dark border border-neutral-100 dark:border-neutral-800',
    outlined: 'bg-transparent border-2 border-neutral-200 dark:border-neutral-700',
    ghost: 'bg-neutral-50 dark:bg-neutral-800/50 border border-transparent'
  };

  const sizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const hoverClasses = hover ? 'hover:shadow-apple-hover dark:hover:shadow-apple-dark-hover hover:-translate-y-1' : '';
  const interactiveClasses = interactive ? 'cursor-pointer focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2' : '';

  const cardClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${sizeClasses[size]}
    ${hoverClasses}
    ${interactiveClasses}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  const motionProps = {
    whileHover: hover || interactive ? animations.cardHover : undefined,
    whileTap: interactive ? animations.buttonTap : undefined,
    ...props
  };

  return (
    <motion.div
      className={cardClasses}
      {...motionProps}
    >
      {children}
    </motion.div>
  );
}

// Specialized card variants for common use cases
export function ArticleCard({ children, className = '', ...props }: Omit<CardProps, 'variant'>) {
  return (
    <Card
      variant="elevated"
      hover
      interactive
      className={`overflow-hidden ${className}`}
      {...props}
    >
      {children}
    </Card>
  );
}

export function FeatureCard({ children, className = '', ...props }: Omit<CardProps, 'variant'>) {
  return (
    <Card
      variant="ghost"
      size="lg"
      className={`text-center ${className}`}
      {...props}
    >
      {children}
    </Card>
  );
}

export function StatsCard({ children, className = '', ...props }: Omit<CardProps, 'variant'>) {
  return (
    <Card
      variant="outlined"
      size="sm"
      className={`text-center ${className}`}
      {...props}
    >
      {children}
    </Card>
  );
}
