import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { Image } from '../media/Image';

interface AppleCardProps {
  title: string;
  subtitle?: string;
  description?: string;
  image?: string;
  imageAlt?: string;
  href?: string;
  onClick?: () => void;
  badge?: {
    text: string;
    variant?: 'primary' | 'secondary' | 'success' | 'warning';
  };
  rating?: number;
  price?: string;
  author?: {
    name: string;
    avatar?: string;
  };
  date?: string;
  readTime?: string;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'featured' | 'minimal';
}

export function AppleCard({
  title,
  subtitle,
  description,
  image,
  imageAlt = '',
  href,
  onClick,
  badge,
  rating,
  price,
  author,
  date,
  readTime,
  className = '',
  size = 'medium',
  variant = 'default',
}: AppleCardProps) {
  const getBadgeColor = (variant: string) => {
    switch (variant) {
      case 'primary':
        return 'bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-400';
      case 'secondary':
        return 'bg-secondary-100 text-secondary-800 dark:bg-secondary-900/20 dark:text-secondary-400';
      case 'success':
        return 'bg-success-100 text-success-800 dark:bg-success-900/20 dark:text-success-400';
      case 'warning':
        return 'bg-warning-100 text-warning-800 dark:bg-warning-900/20 dark:text-warning-400';
      default:
        return 'bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-400';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'p-4';
      case 'large':
        return 'p-8';
      default:
        return 'p-6';
    }
  };

  const getImageAspect = () => {
    switch (variant) {
      case 'featured':
        return 'aspect-[16/10]';
      case 'minimal':
        return 'aspect-square';
      default:
        return 'aspect-video';
    }
  };

  const CardContent = () => (
    <motion.div
      whileHover={{ y: -4, scale: 1.02 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={`group bg-white dark:bg-neutral-800 rounded-2xl shadow-apple dark:shadow-apple-dark border border-neutral-200/50 dark:border-neutral-700/50 overflow-hidden hover:shadow-apple-hover dark:hover:shadow-apple-dark-hover transition-all duration-300 ${className}`}
    >
      {/* Image */}
      {image && (
        <div className={`relative ${getImageAspect()} overflow-hidden`}>
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="w-full h-full"
          >
            <Image
              src={image}
              alt={imageAlt}
              className="w-full h-full object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          </motion.div>
          
          {/* Overlays */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          {/* Badge */}
          {badge && (
            <div className="absolute top-4 left-4">
              <span className={`inline-flex px-3 py-1.5 text-xs font-medium rounded-full backdrop-blur-sm ${getBadgeColor(badge.variant || 'primary')}`}>
                {badge.text}
              </span>
            </div>
          )}

          {/* Rating */}
          {rating && (
            <div className="absolute top-4 right-4 bg-white/90 dark:bg-neutral-800/90 backdrop-blur-sm rounded-lg px-2 py-1 shadow-sm">
              <div className="flex items-center space-x-1">
                <span className="text-sm font-semibold text-neutral-900 dark:text-neutral-100">
                  {rating.toFixed(1)}
                </span>
                <div className="w-4 h-4 text-warning-400">★</div>
              </div>
            </div>
          )}

          {/* Price */}
          {price && (
            <div className="absolute bottom-4 right-4 bg-white/90 dark:bg-neutral-800/90 backdrop-blur-sm rounded-lg px-3 py-1.5 shadow-sm">
              <span className="text-sm font-bold text-neutral-900 dark:text-neutral-100">
                {price}
              </span>
            </div>
          )}
        </div>
      )}
      
      {/* Content */}
      <div className={getSizeClasses()}>
        {/* Metadata */}
        {(date || readTime) && (
          <div className="flex items-center space-x-3 text-sm text-neutral-500 dark:text-neutral-400 mb-3">
            {date && <time>{date}</time>}
            {date && readTime && <span>•</span>}
            {readTime && <span>{readTime}</span>}
          </div>
        )}
        
        {/* Subtitle */}
        {subtitle && (
          <p className="text-sm font-medium text-primary-600 dark:text-primary-400 mb-2">
            {subtitle}
          </p>
        )}
        
        {/* Title */}
        <h3 className={`font-bold text-neutral-900 dark:text-neutral-100 mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors line-clamp-2 ${
          size === 'large' ? 'text-2xl' : size === 'small' ? 'text-lg' : 'text-xl'
        }`}>
          {title}
        </h3>
        
        {/* Description */}
        {description && (
          <p className="text-neutral-600 dark:text-neutral-400 line-clamp-3 mb-4 leading-relaxed">
            {description}
          </p>
        )}
        
        {/* Footer */}
        <div className="flex items-center justify-between">
          {/* Author */}
          {author && (
            <div className="flex items-center space-x-3">
              {author.avatar && (
                <img
                  src={author.avatar}
                  alt={author.name}
                  className="w-8 h-8 rounded-full object-cover"
                />
              )}
              <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                {author.name}
              </span>
            </div>
          )}
          
          {/* Arrow */}
          <motion.div
            whileHover={{ x: 4 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowRight className="w-5 h-5 text-neutral-400 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors" />
          </motion.div>
        </div>
      </div>
    </motion.div>
  );

  if (href) {
    return (
      <a href={href} onClick={onClick} className="block focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 rounded-2xl">
        <CardContent />
      </a>
    );
  }

  if (onClick) {
    return (
      <button onClick={onClick} className="block w-full text-left focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 rounded-2xl">
        <CardContent />
      </button>
    );
  }

  return <CardContent />;
}