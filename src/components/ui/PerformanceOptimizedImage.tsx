import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

interface PerformanceOptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  sizes?: string;
  priority?: boolean;
  placeholder?: string;
  onLoad?: () => void;
  onError?: () => void;
  quality?: number;
}

export function PerformanceOptimizedImage({
  src,
  alt,
  className = '',
  sizes,
  priority = false,
  placeholder,
  onLoad,
  onError,
  quality = 85,
}: PerformanceOptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observerRef.current?.disconnect();
        }
      },
      { rootMargin: '50px' }
    );

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => observerRef.current?.disconnect();
  }, [priority, isInView]);

  // Generate optimized URLs for different formats
  const generateOptimizedSrc = (originalSrc: string, format?: 'webp' | 'avif') => {
    if (!originalSrc.includes('pexels.com')) {
      return originalSrc;
    }

    const url = new URL(originalSrc);
    url.searchParams.set('auto', 'compress');
    url.searchParams.set('cs', 'tinysrgb');
    
    if (format) {
      url.searchParams.set('fm', format);
    }
    
    if (quality !== 85) {
      url.searchParams.set('q', quality.toString());
    }

    return url.toString();
  };

  const generateSrcSet = (baseSrc: string) => {
    if (!baseSrc.includes('pexels.com')) {
      return undefined;
    }

    const widths = [400, 800, 1200, 1600, 2000];
    return widths
      .map(width => {
        const url = new URL(baseSrc);
        url.searchParams.set('w', width.toString());
        return `${url.toString()} ${width}w`;
      })
      .join(', ');
  };

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  if (hasError) {
    return (
      <div className={`bg-neutral-200 dark:bg-neutral-700 flex items-center justify-center ${className}`}>
        <div className="text-center p-4">
          <div className="w-12 h-12 bg-neutral-300 dark:bg-neutral-600 rounded-lg mx-auto mb-2"></div>
          <p className="text-sm text-neutral-500 dark:text-neutral-400">
            Image failed to load
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative overflow-hidden ${className}`} ref={imgRef}>
      {/* Placeholder/Blur backdrop */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-neutral-200 dark:bg-neutral-700">
          {placeholder && (
            <img
              src={placeholder}
              alt=""
              className="w-full h-full object-cover blur-sm scale-110"
              aria-hidden="true"
            />
          )}
          
          {/* Loading shimmer */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent">
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
              animate={{ x: ['-100%', '100%'] }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: 'linear'
              }}
            />
          </div>
        </div>
      )}

      {/* Main image with modern format support */}
      {isInView && (
        <picture>
          {/* AVIF format for modern browsers */}
          <source
            srcSet={generateSrcSet(generateOptimizedSrc(src, 'avif'))}
            sizes={sizes || "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}
            type="image/avif"
          />
          
          {/* WebP format fallback */}
          <source
            srcSet={generateSrcSet(generateOptimizedSrc(src, 'webp'))}
            sizes={sizes || "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}
            type="image/webp"
          />
          
          {/* JPEG fallback */}
          <motion.img
            src={generateOptimizedSrc(src)}
            srcSet={generateSrcSet(src)}
            sizes={sizes || "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}
            alt={alt}
            onLoad={handleLoad}
            onError={handleError}
            loading={priority ? "eager" : "lazy"}
            decoding="async"
            initial={{ opacity: 0 }}
            animate={{ opacity: isLoaded ? 1 : 0 }}
            transition={{ duration: 0.3 }}
            className={`w-full h-full object-cover ${isLoaded ? 'relative' : 'absolute inset-0'}`}
          />
        </picture>
      )}
    </div>
  );
}