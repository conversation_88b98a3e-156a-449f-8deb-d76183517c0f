import React from 'react';
import { motion } from 'framer-motion';
import { Star, ExternalLink, ShoppingCart, Check, X } from 'lucide-react';
import { ReviewData } from '../../data/schema';
import { trackEvent, AnalyticsEvents } from '../../lib/analytics';
import { animations } from '../../utils/animations';
import { AnimatedButton } from '../ui/AnimatedButton';

interface EnhancedVerdictCardProps {
  reviewData: ReviewData;
  className?: string;
}

export function EnhancedVerdictCard({ reviewData, className = '' }: EnhancedVerdictCardProps) {
  const { product_name, brand, rating, pros, cons, verdict, affiliate_links, price } = reviewData;

  const handleAffiliateClick = (store: string, url: string) => {
    trackEvent(AnalyticsEvents.AFFILIATE_LINK_CLICK, {
      location: 'EnhancedVerdictCard',
      component_id: `verdict-card-${product_name.toLowerCase().replace(/\s+/g, '-')}`,
      product_name,
      store,
      price: price?.current || price?.msrp,
    });
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    return (
      <div className="flex items-center space-x-1">
        {/* Full stars */}
        {Array.from({ length: fullStars }).map((_, i) => (
          <motion.div
            key={`full-${i}`}
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: i * 0.1, duration: 0.3 }}
          >
            <Star className="w-5 h-5 fill-warning-400 text-warning-400" />
          </motion.div>
        ))}
        
        {/* Half star */}
        {hasHalfStar && (
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: fullStars * 0.1, duration: 0.3 }}
            className="relative"
          >
            <Star className="w-5 h-5 text-neutral-300 dark:text-neutral-600" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <Star className="w-5 h-5 fill-warning-400 text-warning-400" />
            </div>
          </motion.div>
        )}
        
        {/* Empty stars */}
        {Array.from({ length: emptyStars }).map((_, i) => (
          <motion.div
            key={`empty-${i}`}
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: (fullStars + (hasHalfStar ? 1 : 0) + i) * 0.1, duration: 0.3 }}
          >
            <Star className="w-5 h-5 text-neutral-300 dark:text-neutral-600" />
          </motion.div>
        ))}
        
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="ml-2 font-semibold text-lg text-neutral-900 dark:text-neutral-100"
        >
          {rating.overall.toFixed(1)}
        </motion.span>
      </div>
    );
  };

  const affiliateButtons = [
    { key: 'amazon', label: 'Amazon', url: affiliate_links?.amazon },
    { key: 'best_buy', label: 'Best Buy', url: affiliate_links?.best_buy },
    { key: 'direct', label: brand, url: affiliate_links?.direct },
  ].filter(button => button.url);

  return (
    <motion.div
      variants={animations.verdictEntrance}
      initial="initial"
      animate="animate"
      className={`bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark p-6 ${className}`}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-6">
        <div>
          <motion.h3
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-1"
          >
            Our Verdict
          </motion.h3>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-neutral-600 dark:text-neutral-400"
          >
            {brand} {product_name}
          </motion.p>
        </div>
        
        {price && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3 }}
            className="text-right"
          >
            <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
              ${price.current || price.msrp}
            </div>
            {price.current && price.current < price.msrp && (
              <div className="text-sm text-neutral-500 dark:text-neutral-400 line-through">
                ${price.msrp}
              </div>
            )}
          </motion.div>
        )}
      </div>

      {/* Rating */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
        className="mb-6"
      >
        {renderStars(rating.overall)}
      </motion.div>

      {/* Verdict Text */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="mb-6"
      >
        <p className="text-neutral-700 dark:text-neutral-300 leading-relaxed">
          {verdict}
        </p>
      </motion.div>

      {/* Pros and Cons */}
      <div className="grid md:grid-cols-2 gap-6 mb-6">
        {/* Pros */}
        <div>
          <motion.h4
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
            className="font-semibold text-success-700 dark:text-success-400 mb-3 flex items-center"
          >
            <div className="w-6 h-6 bg-success-100 dark:bg-success-900/20 rounded-full flex items-center justify-center mr-3">
              <Check className="w-4 h-4 text-success-600 dark:text-success-400" />
            </div>
            Pros
          </motion.h4>
          <ul className="space-y-2">
            {pros.map((pro, index) => (
              <motion.li
                key={index}
                custom={index}
                variants={animations.prosConsItem}
                initial="initial"
                animate="animate"
                className="text-sm text-neutral-700 dark:text-neutral-300 flex items-start"
              >
                <Check className="w-4 h-4 text-success-500 mt-0.5 mr-3 flex-shrink-0" />
                {pro}
              </motion.li>
            ))}
          </ul>
        </div>

        {/* Cons */}
        <div>
          <motion.h4
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.7 }}
            className="font-semibold text-error-700 dark:text-error-400 mb-3 flex items-center"
          >
            <div className="w-6 h-6 bg-error-100 dark:bg-error-900/20 rounded-full flex items-center justify-center mr-3">
              <X className="w-4 h-4 text-error-600 dark:text-error-400" />
            </div>
            Cons
          </motion.h4>
          <ul className="space-y-2">
            {cons.map((con, index) => (
              <motion.li
                key={index}
                custom={pros.length + index}
                variants={animations.prosConsItem}
                initial="initial"
                animate="animate"
                className="text-sm text-neutral-700 dark:text-neutral-300 flex items-start"
              >
                <X className="w-4 h-4 text-error-500 mt-0.5 mr-3 flex-shrink-0" />
                {con}
              </motion.li>
            ))}
          </ul>
        </div>
      </div>

      {/* Category Ratings */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="mb-6"
      >
        <h4 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-3">
          Detailed Ratings
        </h4>
        <div className="grid grid-cols-2 gap-4">
          {Object.entries(rating.categories).map(([category, score], index) => (
            <motion.div
              key={category}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.9 + index * 0.1 }}
              className="flex items-center justify-between"
            >
              <span className="text-sm text-neutral-600 dark:text-neutral-400 capitalize">
                {category}
              </span>
              <div className="flex items-center space-x-1">
                <span className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                  {score.toFixed(1)}
                </span>
                <Star className="w-4 h-4 fill-warning-400 text-warning-400" />
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Purchase Buttons */}
      {affiliateButtons.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
          className="border-t border-neutral-200 dark:border-neutral-700 pt-6"
        >
          <h4 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-3 flex items-center">
            <ShoppingCart className="w-4 h-4 mr-2" />
            Where to Buy
          </h4>
          <div className="flex flex-wrap gap-3">
            {affiliateButtons.map(({ key, label, url }, index) => (
              <motion.div
                key={key}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.1 + index * 0.1 }}
              >
                <AnimatedButton
                  onClick={() => handleAffiliateClick(key, url!)}
                  variant={index === 0 ? 'primary' : 'secondary'}
                  size="sm"
                  className="text-sm"
                >
                  {label}
                  <ExternalLink className="w-4 h-4 ml-2" />
                </AnimatedButton>
              </motion.div>
            ))}
          </div>
          
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.3 }}
            className="text-xs text-neutral-500 dark:text-neutral-400 mt-3"
          >
            * We may earn a commission from purchases made through these links
          </motion.p>
        </motion.div>
      )}
    </motion.div>
  );
}