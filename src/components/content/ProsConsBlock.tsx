import React from 'react';
import { motion } from 'framer-motion';
import { Check, X } from 'lucide-react';

interface ProsConsBlockProps {
  pros: string[];
  cons: string[];
  className?: string;
}

export function ProsConsBlock({ pros, cons, className = '' }: ProsConsBlockProps) {
  return (
    <div className={`bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark p-6 ${className}`}>
      <div className="grid md:grid-cols-2 gap-8">
        {/* Pros */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h3 className="font-bold text-lg text-success-700 dark:text-success-400 mb-4 flex items-center">
            <div className="w-6 h-6 bg-success-100 dark:bg-success-900/20 rounded-full flex items-center justify-center mr-3">
              <Check className="w-4 h-4 text-success-600 dark:text-success-400" />
            </div>
            Pros
          </h3>
          <ul className="space-y-3">
            {pros.map((pro, index) => (
              <motion.li
                key={index}
                initial={{ opacity: 0, x: -10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start text-neutral-700 dark:text-neutral-300"
              >
                <Check className="w-5 h-5 text-success-500 mt-0.5 mr-3 flex-shrink-0" />
                <span>{pro}</span>
              </motion.li>
            ))}
          </ul>
        </motion.div>

        {/* Cons */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          viewport={{ once: true }}
        >
          <h3 className="font-bold text-lg text-error-700 dark:text-error-400 mb-4 flex items-center">
            <div className="w-6 h-6 bg-error-100 dark:bg-error-900/20 rounded-full flex items-center justify-center mr-3">
              <X className="w-4 h-4 text-error-600 dark:text-error-400" />
            </div>
            Cons
          </h3>
          <ul className="space-y-3">
            {cons.map((con, index) => (
              <motion.li
                key={index}
                initial={{ opacity: 0, x: 10 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start text-neutral-700 dark:text-neutral-300"
              >
                <X className="w-5 h-5 text-error-500 mt-0.5 mr-3 flex-shrink-0" />
                <span>{con}</span>
              </motion.li>
            ))}
          </ul>
        </motion.div>
      </div>
    </div>
  );
}