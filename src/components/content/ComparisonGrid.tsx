import React from 'react';
import { motion } from 'framer-motion';
import { AffiliateProductCard } from './AffiliateProductCard';

interface ComparisonGridProps {
  products: Array<{
    id: string;
    name: string;
    brand: string;
    image: string;
    price: {
      current: number;
      msrp?: number;
      currency: string;
    };
    rating: number;
    specs: string[];
    pros: string[];
    cons: string[];
    affiliateLinks: {
      amazon?: string;
      bestBuy?: string;
      direct?: string;
    };
  }>;
  title?: string;
  className?: string;
}

export function ComparisonGrid({ products, title, className = '' }: ComparisonGridProps) {
  return (
    <section className={`py-12 ${className}`}>
      {title && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 tracking-tight">
            {title}
          </h2>
        </motion.div>
      )}

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {products.map((product, index) => (
          <motion.div
            key={product.id}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
          >
            <AffiliateProductCard
              product={product}
              variant="comparison"
            />
          </motion.div>
        ))}
      </div>
    </section>
  );
}