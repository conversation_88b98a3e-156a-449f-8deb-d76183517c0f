import React from 'react';
import { motion } from 'framer-motion';
import { Info } from 'lucide-react';

interface AffiliateDisclosureProps {
  className?: string;
  variant?: 'banner' | 'inline' | 'footer';
}

export function AffiliateDisclosure({ 
  className = '', 
  variant = 'banner' 
}: AffiliateDisclosureProps) {
  const content = {
    banner: {
      title: 'Affiliate Disclosure',
      text: 'This article contains affiliate links. We may earn a commission from purchases made through these links, at no additional cost to you. This helps support our editorial work and allows us to continue providing unbiased reviews.',
    },
    inline: {
      title: '',
      text: 'This post contains affiliate links, which means we may earn a small commission if you make a purchase through our links.',
    },
    footer: {
      title: '',
      text: 'Some links in this article are affiliate links. We may earn a commission from qualifying purchases.',
    },
  };

  const { title, text } = content[variant];

  const getBannerStyle = () => {
    switch (variant) {
      case 'banner':
        return 'bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4';
      case 'inline':
        return 'bg-neutral-100 dark:bg-neutral-800 rounded-lg p-3';
      case 'footer':
        return 'text-sm text-neutral-600 dark:text-neutral-400 border-t border-neutral-200 dark:border-neutral-700 pt-4';
      default:
        return '';
    }
  };

  if (variant === 'footer') {
    return (
      <div className={`${getBannerStyle()} ${className}`}>
        <p className="flex items-start">
          <Info className="w-4 h-4 mr-2 mt-0.5 text-neutral-500 flex-shrink-0" />
          {text}
        </p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`${getBannerStyle()} ${className}`}
      role="note"
      aria-label="Affiliate disclosure"
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <Info className="w-5 h-5 text-primary-600 dark:text-primary-400 mt-0.5" />
        </div>
        <div className="ml-3">
          {title && (
            <h4 className="font-semibold text-primary-900 dark:text-primary-100 mb-1">
              {title}
            </h4>
          )}
          <p className="text-sm text-primary-800 dark:text-primary-200 leading-relaxed">
            {text}
          </p>
        </div>
      </div>
    </motion.div>
  );
}