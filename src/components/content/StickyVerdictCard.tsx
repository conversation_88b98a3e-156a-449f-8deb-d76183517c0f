import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, ExternalLink, ShoppingCart, X, ChevronUp } from 'lucide-react';
import { ReviewData } from '../../data/schema';
import { trackEvent, AnalyticsEvents } from '../../lib/analytics';

interface StickyVerdictCardProps {
  reviewData: ReviewData;
  className?: string;
}

export function StickyVerdictCard({ reviewData, className = '' }: StickyVerdictCardProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const { product_name, brand, rating, verdict, affiliate_links, price } = reviewData;

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      
      // Show when scrolled past hero section but not at the very bottom
      const showThreshold = windowHeight * 0.5;
      const hideThreshold = documentHeight - windowHeight - 200;
      
      setIsVisible(scrollPosition > showThreshold && scrollPosition < hideThreshold);
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleAffiliateClick = (store: string, url: string) => {
    trackEvent(AnalyticsEvents.AFFILIATE_LINK_CLICK, {
      location: 'StickyVerdictCard',
      component_id: `sticky-verdict-${product_name.toLowerCase().replace(/\s+/g, '-')}`,
      product_name,
      store,
      price: price?.current || price?.msrp,
    });
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    return (
      <div className="flex items-center space-x-1">
        {Array.from({ length: fullStars }).map((_, i) => (
          <Star key={`full-${i}`} className="w-4 h-4 fill-warning-400 text-warning-400" />
        ))}
        {hasHalfStar && (
          <div className="relative">
            <Star className="w-4 h-4 text-neutral-300 dark:text-neutral-600" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <Star className="w-4 h-4 fill-warning-400 text-warning-400" />
            </div>
          </div>
        )}
        {Array.from({ length: emptyStars }).map((_, i) => (
          <Star key={`empty-${i}`} className="w-4 h-4 text-neutral-300 dark:text-neutral-600" />
        ))}
      </div>
    );
  };

  const primaryAffiliate = affiliate_links?.amazon || affiliate_links?.best_buy || affiliate_links?.direct;

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: isMobile ? 100 : 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: isMobile ? 100 : 20, scale: 0.95 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className={`${
          isMobile 
            ? 'fixed bottom-4 left-4 right-4 z-40' 
            : 'fixed top-24 right-4 w-80 z-40'
        } ${className}`}
      >
        <div className="bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark overflow-hidden">
          {/* Header */}
          <div className="p-4 border-b border-neutral-200 dark:border-neutral-700">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-neutral-900 dark:text-neutral-100 text-sm line-clamp-1">
                  {brand} {product_name}
                </h3>
                <div className="flex items-center mt-1">
                  {renderStars(rating.overall)}
                  <span className="ml-2 text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    {rating.overall.toFixed(1)}
                  </span>
                </div>
              </div>
              
              {price && (
                <div className="text-right ml-4">
                  <div className="text-lg font-bold text-primary-600 dark:text-primary-400">
                    ${price.current || price.msrp}
                  </div>
                  {price.current && price.current < price.msrp && (
                    <div className="text-xs text-neutral-500 dark:text-neutral-400 line-through">
                      ${price.msrp}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Expandable Content (Mobile) */}
          {isMobile && (
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0">
                    <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-3 mb-4">
                      {verdict}
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          )}

          {/* Desktop Content */}
          {!isMobile && (
            <div className="p-4">
              <p className="text-sm text-neutral-600 dark:text-neutral-400 line-clamp-3 mb-4">
                {verdict}
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="p-4 pt-0 space-y-3">
            {primaryAffiliate && (
              <button
                onClick={() => handleAffiliateClick('primary', primaryAffiliate)}
                className="w-full inline-flex items-center justify-center px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-all hover:scale-105 shadow-apple"
              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                Buy Now
                <ExternalLink className="w-4 h-4 ml-2" />
              </button>
            )}
            
            <div className="flex items-center justify-between">
              {isMobile && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
                >
                  {isExpanded ? 'Less' : 'More'}
                  <ChevronUp className={`w-4 h-4 ml-1 transition-transform ${isExpanded ? '' : 'rotate-180'}`} />
                </button>
              )}
              
              <button
                onClick={() => setIsVisible(false)}
                className="p-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300 transition-colors rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700"
                aria-label="Close verdict card"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}