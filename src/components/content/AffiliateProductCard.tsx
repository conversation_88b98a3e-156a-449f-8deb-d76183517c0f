import React from 'react';
import { motion } from 'framer-motion';
import { Star, ExternalLink, ShoppingCart, Check, X } from 'lucide-react';
import { Image } from '../media/Image';
import { trackEvent, AnalyticsEvents } from '../../lib/analytics';

interface AffiliateProductCardProps {
  product: {
    id: string;
    name: string;
    brand: string;
    image: string;
    price: {
      current: number;
      msrp?: number;
      currency: string;
    };
    rating: number;
    specs: string[];
    pros: string[];
    cons: string[];
    affiliateLinks: {
      amazon?: string;
      bestBuy?: string;
      direct?: string;
    };
  };
  variant?: 'default' | 'comparison' | 'featured';
  className?: string;
}

export function AffiliateProductCard({ 
  product, 
  variant = 'default', 
  className = '' 
}: AffiliateProductCardProps) {
  const { name, brand, image, price, rating, specs, pros, cons, affiliateLinks } = product;

  const handleAffiliateClick = (store: string, url: string) => {
    trackEvent(AnalyticsEvents.AFFILIATE_LINK_CLICK, {
      location: 'AffiliateProductCard',
      component_id: `product-card-${product.id}`,
      product_name: `${brand} ${name}`,
      store,
      price: price.current,
    });
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const renderStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    return (
      <div className="flex items-center space-x-1">
        {Array.from({ length: fullStars }).map((_, i) => (
          <Star key={`full-${i}`} className="w-4 h-4 fill-warning-400 text-warning-400" />
        ))}
        {hasHalfStar && (
          <div className="relative">
            <Star className="w-4 h-4 text-neutral-300 dark:text-neutral-600" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <Star className="w-4 h-4 fill-warning-400 text-warning-400" />
            </div>
          </div>
        )}
        {Array.from({ length: emptyStars }).map((_, i) => (
          <Star key={`empty-${i}`} className="w-4 h-4 text-neutral-300 dark:text-neutral-600" />
        ))}
        <span className="ml-2 text-sm font-medium text-neutral-700 dark:text-neutral-300">
          {rating.toFixed(1)}
        </span>
      </div>
    );
  };

  const getCardSize = () => {
    switch (variant) {
      case 'comparison':
        return 'p-6';
      case 'featured':
        return 'p-8';
      default:
        return 'p-6';
    }
  };

  const primaryAffiliate = affiliateLinks.amazon || affiliateLinks.bestBuy || affiliateLinks.direct;

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
      className={`bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50 shadow-apple dark:shadow-apple-dark hover:shadow-apple-hover dark:hover:shadow-apple-dark-hover transition-all duration-300 ${getCardSize()} ${className}`}
    >
      {/* Product Image */}
      <div className="aspect-square rounded-xl overflow-hidden mb-6 bg-neutral-50 dark:bg-neutral-700">
        <Image
          src={image}
          alt={`${brand} ${name}`}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Product Info */}
      <div className="space-y-4">
        <div>
          <h3 className="font-bold text-lg text-neutral-900 dark:text-neutral-100 mb-1">
            {brand} {name}
          </h3>
          {renderStars(rating)}
        </div>

        {/* Price */}
        <div className="flex items-baseline space-x-2">
          <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
            ${price.current}
          </span>
          {price.msrp && price.current < price.msrp && (
            <span className="text-sm text-neutral-500 dark:text-neutral-400 line-through">
              ${price.msrp}
            </span>
          )}
        </div>

        {/* Specs */}
        {specs.length > 0 && (
          <div>
            <h4 className="font-medium text-neutral-900 dark:text-neutral-100 mb-2">
              Key Specs
            </h4>
            <ul className="space-y-1">
              {specs.slice(0, 3).map((spec, index) => (
                <li key={index} className="text-sm text-neutral-600 dark:text-neutral-400 flex items-start">
                  <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mt-2 mr-2 flex-shrink-0" />
                  {spec}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Pros & Cons (for comparison variant) */}
        {variant === 'comparison' && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-success-700 dark:text-success-400 mb-2 text-sm">
                Pros
              </h4>
              <ul className="space-y-1">
                {pros.slice(0, 2).map((pro, index) => (
                  <li key={index} className="text-xs text-neutral-600 dark:text-neutral-400 flex items-start">
                    <Check className="w-3 h-3 text-success-500 mt-0.5 mr-1 flex-shrink-0" />
                    {pro}
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-error-700 dark:text-error-400 mb-2 text-sm">
                Cons
              </h4>
              <ul className="space-y-1">
                {cons.slice(0, 2).map((con, index) => (
                  <li key={index} className="text-xs text-neutral-600 dark:text-neutral-400 flex items-start">
                    <X className="w-3 h-3 text-error-500 mt-0.5 mr-1 flex-shrink-0" />
                    {con}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {/* CTA Button */}
        {primaryAffiliate && (
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => handleAffiliateClick('primary', primaryAffiliate)}
            className="w-full inline-flex items-center justify-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-xl transition-all shadow-apple hover:shadow-apple-hover"
          >
            <ShoppingCart className="w-4 h-4 mr-2" />
            Check Price
            <ExternalLink className="w-4 h-4 ml-2" />
          </motion.button>
        )}

        {/* Multiple Store Links */}
        {Object.keys(affiliateLinks).length > 1 && (
          <div className="flex space-x-2">
            {affiliateLinks.amazon && (
              <button
                onClick={() => handleAffiliateClick('amazon', affiliateLinks.amazon!)}
                className="flex-1 px-3 py-2 text-xs font-medium bg-neutral-100 dark:bg-neutral-700 hover:bg-neutral-200 dark:hover:bg-neutral-600 text-neutral-700 dark:text-neutral-300 rounded-lg transition-colors"
              >
                Amazon
              </button>
            )}
            {affiliateLinks.bestBuy && (
              <button
                onClick={() => handleAffiliateClick('bestBuy', affiliateLinks.bestBuy!)}
                className="flex-1 px-3 py-2 text-xs font-medium bg-neutral-100 dark:bg-neutral-700 hover:bg-neutral-200 dark:hover:bg-neutral-600 text-neutral-700 dark:text-neutral-300 rounded-lg transition-colors"
              >
                Best Buy
              </button>
            )}
          </div>
        )}
      </div>
    </motion.div>
  );
}