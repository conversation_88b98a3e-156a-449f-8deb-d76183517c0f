import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Check, AlertCircle, Sparkles } from 'lucide-react';
import { trackEvent, AnalyticsEvents } from '../../lib/analytics';

interface NewsletterSignupProps {
  variant?: 'inline' | 'modal' | 'footer';
  className?: string;
}

export function NewsletterSignup({ variant = 'inline', className = '' }: NewsletterSignupProps) {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setErrorMessage('Please enter your email address');
      setStatus('error');
      return;
    }

    if (!validateEmail(email)) {
      setErrorMessage('Please enter a valid email address');
      setStatus('error');
      return;
    }

    setStatus('loading');
    setErrorMessage('');

    try {
      // Track newsletter signup attempt
      trackEvent(AnalyticsEvents.NEWSLETTER_SIGNUP, {
        location: variant,
        email_domain: email.split('@')[1],
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For demo purposes, randomly succeed or fail
      if (Math.random() > 0.2) {
        setStatus('success');
        setEmail('');
        
        // Track successful signup
        trackEvent(AnalyticsEvents.NEWSLETTER_SIGNUP_SUCCESS, {
          location: variant,
          email_domain: email.split('@')[1],
        });
      } else {
        throw new Error('Subscription failed');
      }
    } catch (error) {
      setStatus('error');
      setErrorMessage('Something went wrong. Please try again.');
      
      // Track error
      trackEvent(AnalyticsEvents.NEWSLETTER_SIGNUP_ERROR, {
        location: variant,
        error_message: 'Subscription failed',
      });
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'modal':
        return 'bg-white dark:bg-neutral-800 p-8 rounded-3xl shadow-apple dark:shadow-apple-dark max-w-md mx-auto border border-neutral-200 dark:border-neutral-700';
      case 'footer':
        return 'bg-neutral-800 p-8 rounded-3xl';
      default:
        return 'bg-white dark:bg-neutral-800 rounded-3xl p-8 lg:p-12 shadow-apple dark:shadow-apple-dark border border-neutral-200/50 dark:border-neutral-700/50';
    }
  };

  const content = {
    title: variant === 'footer' ? 'Stay Updated' : 'Get the Latest Tech Insights',
    subtitle: variant === 'footer' 
      ? 'Subscribe to our newsletter for the latest reviews and tech news.'
      : 'Join thousands of tech enthusiasts who get our weekly digest of reviews, guides, and industry insights delivered straight to their inbox.',
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className={`${getVariantStyles()} ${className}`}
    >
      <div className="text-center mb-8">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl mb-6 shadow-apple"
        >
          <Mail className="w-8 h-8 text-white" />
        </motion.div>
        
        <h3 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 tracking-tight">
          {content.title}
        </h3>
        
        <p className="text-lg text-neutral-600 dark:text-neutral-400 leading-relaxed max-w-2xl mx-auto">
          {content.subtitle}
        </p>
      </div>

      {status === 'success' ? (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4 }}
          className="text-center py-8"
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-success-500 to-success-600 rounded-2xl mb-6 shadow-apple">
            <Check className="w-8 h-8 text-white" />
          </div>
          <h4 className="text-2xl font-bold text-success-900 dark:text-success-100 mb-3">
            Welcome aboard! 🎉
          </h4>
          <p className="text-lg text-success-700 dark:text-success-300">
            Check your inbox for a confirmation email and get ready for amazing tech insights.
          </p>
        </motion.div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="newsletter-email" className="sr-only">
              Email address
            </label>
            <div className="relative">
              <input
                type="email"
                id="newsletter-email"
                placeholder="Enter your email address"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (status === 'error') {
                    setStatus('idle');
                    setErrorMessage('');
                  }
                }}
                disabled={status === 'loading'}
                className={`w-full px-6 py-4 text-lg bg-neutral-50 dark:bg-neutral-700 border-0 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:bg-white dark:focus:bg-neutral-600 transition-all placeholder-neutral-500 dark:placeholder-neutral-400 ${
                  status === 'error' 
                    ? 'ring-2 ring-error-300 dark:ring-error-600' 
                    : ''
                } ${
                  status === 'loading' ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                aria-describedby={status === 'error' ? 'email-error' : undefined}
              />
              
              {status === 'loading' && (
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-6 h-6 border-2 border-primary-500 border-t-transparent rounded-full"
                  />
                </div>
              )}
            </div>
            
            {status === 'error' && errorMessage && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                id="email-error"
                className="flex items-center mt-3 text-sm text-error-600 dark:text-error-400"
                role="alert"
              >
                <AlertCircle className="w-4 h-4 mr-2 flex-shrink-0" />
                {errorMessage}
              </motion.div>
            )}
          </div>

          <motion.button
            type="submit"
            disabled={status === 'loading'}
            whileHover={{ scale: status === 'loading' ? 1 : 1.02 }}
            whileTap={{ scale: status === 'loading' ? 1 : 0.98 }}
            className={`w-full px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold text-lg rounded-2xl transition-all shadow-apple hover:shadow-apple-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
              status === 'loading' ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {status === 'loading' ? (
              <span className="flex items-center justify-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-5 h-5 border-2 border-white border-t-transparent rounded-full mr-3"
                />
                Subscribing...
              </span>
            ) : (
              <span className="flex items-center justify-center">
                <Sparkles className="w-5 h-5 mr-2" />
                Subscribe for Free
              </span>
            )}
          </motion.button>

          <div className="text-center">
            <p className="text-sm text-neutral-500 dark:text-neutral-400 leading-relaxed">
              No spam, ever. Unsubscribe at any time. We respect your privacy and will never share your email.
            </p>
          </div>
        </form>
      )}
    </motion.div>
  );
}