import React from 'react';
import { Article } from '../../data/schema';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
  article?: Article;
  type?: 'website' | 'article' | 'product';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  noindex?: boolean;
}

export function SEO({
  title,
  description,
  keywords = [],
  ogImage,
  canonicalUrl,
  article,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = [],
  noindex = false
}: SEOProps) {
  const siteTitle = 'Apex Tech - Expert Tech Reviews & Analysis';
  const siteDescription = 'The definitive source for in-depth tech reviews, comparisons, and buying guides. Unbiased analysis of the latest gadgets and gear.';
  const siteUrl = 'https://apextech.com';

  const finalTitle = article?.seo?.title || title || siteTitle;
  const finalDescription = article?.seo?.description || description || siteDescription;
  const finalKeywords = article?.seo?.keywords || keywords;
  const finalOgImage = article?.seo?.og_image || ogImage || `${siteUrl}/og-default.jpg`;
  
  // Generate canonical URL with fallback logic
  const generateCanonicalUrl = () => {
    if (article?.seo?.canonical_url) return article.seo.canonical_url;
    if (canonicalUrl) return canonicalUrl;
    if (typeof window !== 'undefined') return window.location.href;
    if (article) return `${siteUrl}/articles/${article.slug}`;
    return siteUrl;
  };
  
  const finalCanonicalUrl = generateCanonicalUrl();

  const generateStructuredData = () => {
    if (!article) {
      // Website/Organization structured data
      return {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: 'Apex Tech',
        url: siteUrl,
        description: siteDescription,
        publisher: {
          '@type': 'Organization',
          name: 'Apex Tech',
          logo: {
            '@type': 'ImageObject',
            url: `${siteUrl}/logo.png`,
            width: 512,
            height: 512
          },
          sameAs: [
            'https://twitter.com/apextech',
            'https://youtube.com/apextech',
            'https://github.com/apextech'
          ]
        },
        potentialAction: {
          '@type': 'SearchAction',
          target: {
            '@type': 'EntryPoint',
            urlTemplate: `${siteUrl}/?search={search_term_string}`
          },
          'query-input': 'required name=search_term_string'
        }
      };
    }

    const baseData = {
      '@context': 'https://schema.org',
      '@type': article.type === 'review' ? 'Review' : 'Article',
      headline: article.title,
      description: article.excerpt,
      image: {
        '@type': 'ImageObject',
        url: article.featured_image,
        width: 1200,
        height: 630
      },
      datePublished: article.published_date,
      dateModified: article.updated_date || article.published_date,
      author: article.meta.authors.map(author => ({
        '@type': 'Person',
        name: author.name,
        url: author.social?.website || author.social?.linkedin
      })),
      publisher: {
        '@type': 'Organization',
        name: 'Apex Tech',
        logo: {
          '@type': 'ImageObject',
          url: `${siteUrl}/logo.png`,
          width: 512,
          height: 512
        }
      },
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': finalCanonicalUrl
      },
      articleSection: article.category,
      keywords: article.tags.join(', '),
      wordCount: Math.ceil(article.content.length / 5),
      inLanguage: 'en-US'
    };

    if (article.type === 'review' && article.review_data) {
      const reviewData = article.review_data;
      return {
        ...baseData,
        '@type': 'Review',
        itemReviewed: {
          '@type': 'Product',
          name: `${reviewData.brand} ${reviewData.product_name}`,
          brand: {
            '@type': 'Brand',
            name: reviewData.brand
          },
          model: reviewData.model,
          description: article.excerpt,
          image: {
            '@type': 'ImageObject',
            url: article.featured_image,
            width: 1200,
            height: 630
          },
          category: article.category,
          sku: reviewData.model || `${reviewData.brand}-${reviewData.product_name}`.toLowerCase().replace(/\s+/g, '-'),
          mpn: reviewData.model,
          offers: reviewData.price ? {
            '@type': 'Offer',
            price: reviewData.price.current || reviewData.price.msrp,
            priceCurrency: reviewData.price.currency || 'USD',
            availability: 'https://schema.org/InStock',
            priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
            seller: {
              '@type': 'Organization',
              name: reviewData.brand
            },
            url: finalCanonicalUrl
          } : undefined,
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: reviewData.rating.overall,
            bestRating: 5,
            worstRating: 1,
            ratingCount: 1,
            reviewCount: 1
          },
          review: {
            '@type': 'Review',
            reviewRating: {
              '@type': 'Rating',
              ratingValue: reviewData.rating.overall,
              bestRating: 5,
              worstRating: 1
            },
            author: {
              '@type': 'Person',
              name: article.meta.authors[0]?.name || 'Apex Tech Editorial Team'
            }
          }
        },
        reviewRating: {
          '@type': 'Rating',
          ratingValue: reviewData.rating.overall,
          bestRating: 5,
          worstRating: 1
        },
        positiveNotes: reviewData.pros,
        negativeNotes: reviewData.cons,
        reviewBody: article.excerpt
      };
    }

    return baseData;
  };

  const structuredData = generateStructuredData();

  React.useEffect(() => {
    // Update document title
    document.title = finalTitle;

    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      
      meta.setAttribute('content', content);
    };

    // Basic meta tags
    updateMetaTag('description', finalDescription);
    updateMetaTag('keywords', finalKeywords.join(', '));
    
    // Robots meta tag
    const robotsContent = noindex 
      ? 'noindex, nofollow' 
      : 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1';
    updateMetaTag('robots', robotsContent);

    // Open Graph tags
    updateMetaTag('og:title', finalTitle, true);
    updateMetaTag('og:description', finalDescription, true);
    updateMetaTag('og:image', finalOgImage, true);
    updateMetaTag('og:image:width', '1200', true);
    updateMetaTag('og:image:height', '630', true);
    updateMetaTag('og:image:alt', finalTitle, true);
    updateMetaTag('og:url', finalCanonicalUrl, true);
    updateMetaTag('og:type', type, true);
    updateMetaTag('og:site_name', 'Apex Tech', true);
    updateMetaTag('og:locale', 'en_US', true);

    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:site', '@apextech');
    updateMetaTag('twitter:creator', '@apextech');
    updateMetaTag('twitter:title', finalTitle);
    updateMetaTag('twitter:description', finalDescription);
    updateMetaTag('twitter:image', finalOgImage);
    updateMetaTag('twitter:image:alt', finalTitle);

    // Article-specific meta tags
    if (article || type === 'article') {
      const pubTime = article?.published_date || publishedTime;
      const modTime = article?.updated_date || modifiedTime;
      const articleAuthor = article?.meta.authors[0]?.name || author;
      const articleSection = article?.category || section;
      const articleTags = article?.tags || tags;

      if (pubTime) updateMetaTag('article:published_time', pubTime, true);
      if (modTime) updateMetaTag('article:modified_time', modTime, true);
      if (articleAuthor) updateMetaTag('article:author', articleAuthor, true);
      if (articleSection) updateMetaTag('article:section', articleSection, true);
      
      // Clear existing article tags
      const existingTags = document.querySelectorAll('meta[property="article:tag"]');
      existingTags.forEach(tag => tag.remove());
      
      // Add new article tags
      articleTags.forEach(tag => {
        const tagMeta = document.createElement('meta');
        tagMeta.setAttribute('property', 'article:tag');
        tagMeta.setAttribute('content', tag);
        document.head.appendChild(tagMeta);
      });
    }

    // Canonical URL
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', finalCanonicalUrl);

    // Structured Data (JSON-LD)
    let script = document.querySelector('script[type="application/ld+json"]');
    if (!script) {
      script = document.createElement('script');
      script.setAttribute('type', 'application/ld+json');
      document.head.appendChild(script);
    }
    script.textContent = JSON.stringify(structuredData, null, 2);

    // Additional SEO meta tags
    updateMetaTag('theme-color', '#3b82f6');
    updateMetaTag('msapplication-TileColor', '#3b82f6');
    updateMetaTag('apple-mobile-web-app-capable', 'yes');
    updateMetaTag('apple-mobile-web-app-status-bar-style', 'default');
    updateMetaTag('format-detection', 'telephone=no');

    // Preconnect to external domains for performance
    const preconnectDomains = ['https://images.pexels.com', 'https://fonts.googleapis.com', 'https://fonts.gstatic.com'];
    preconnectDomains.forEach(domain => {
      let link = document.querySelector(`link[rel="preconnect"][href="${domain}"]`) as HTMLLinkElement;
      if (!link) {
        link = document.createElement('link');
        link.setAttribute('rel', 'preconnect');
        link.setAttribute('href', domain);
        if (domain.includes('fonts.gstatic.com')) {
          link.setAttribute('crossorigin', '');
        }
        document.head.appendChild(link);
      }
    });

  }, [finalTitle, finalDescription, finalKeywords, finalOgImage, finalCanonicalUrl, structuredData, article, type, publishedTime, modifiedTime, author, section, tags, noindex]);

  return null;
}