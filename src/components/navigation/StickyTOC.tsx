import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { List, ChevronDown, ChevronUp, X } from 'lucide-react';

interface TOCItem {
  id: string;
  title: string;
  level: number;
}

interface StickyTOCProps {
  content: string;
  className?: string;
}

export function StickyTOC({ content, className = '' }: StickyTOCProps) {
  const [tocItems, setTocItems] = useState<TOCItem[]>([]);
  const [activeId, setActiveId] = useState<string>('');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      setIsCollapsed(mobile);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Extract headings from content
  useEffect(() => {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm;
    const items: TOCItem[] = [];
    let match;

    while ((match = headingRegex.exec(content)) !== null) {
      const level = match[1].length;
      const title = match[2].trim();
      const id = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
      
      items.push({ id, title, level });
    }

    setTocItems(items);
    setIsVisible(items.length > 0);
  }, [content]);

  // Track scroll position and update active heading
  useEffect(() => {
    const handleScroll = () => {
      const headings = tocItems.map(item => document.getElementById(item.id)).filter(Boolean);
      
      if (headings.length === 0) return;

      const scrollPosition = window.scrollY + 100;
      
      for (let i = headings.length - 1; i >= 0; i--) {
        const heading = headings[i];
        if (heading && heading.offsetTop <= scrollPosition) {
          setActiveId(heading.id);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [tocItems]);

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const offset = 80;
      const elementPosition = element.offsetTop - offset;
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
      
      if (isMobile) {
        setIsCollapsed(true);
      }
    }
  };

  if (!isVisible) return null;

  return (
    <div className={`${isMobile ? 'fixed bottom-4 right-4 z-40' : 'sticky top-24'} ${className}`}>
      <div className={`bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark overflow-hidden ${
        isMobile ? 'max-w-xs' : 'w-64'
      }`}>
        {/* Header */}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="w-full flex items-center justify-between p-4 text-left hover:bg-neutral-50 dark:hover:bg-neutral-700 transition-colors"
          aria-expanded={!isCollapsed}
          aria-controls="toc-content"
        >
          <div className="flex items-center">
            <List className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3" />
            <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
              {isMobile ? 'Contents' : 'Table of Contents'}
            </h3>
          </div>
          {isCollapsed ? (
            <ChevronDown className="w-5 h-5 text-neutral-500" />
          ) : (
            <ChevronUp className="w-5 h-5 text-neutral-500" />
          )}
        </button>

        {/* Content */}
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <nav className="p-4 pt-0 max-h-96 overflow-y-auto">
                <ul className="space-y-1">
                  {tocItems.map((item) => (
                    <li key={item.id}>
                      <button
                        onClick={() => scrollToHeading(item.id)}
                        className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all hover:bg-neutral-100 dark:hover:bg-neutral-700 ${
                          activeId === item.id
                            ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 font-medium'
                            : 'text-neutral-600 dark:text-neutral-400'
                        }`}
                        style={{ paddingLeft: `${(item.level - 1) * 12 + 12}px` }}
                      >
                        <span className="line-clamp-2">{item.title}</span>
                      </button>
                    </li>
                  ))}
                </ul>
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}