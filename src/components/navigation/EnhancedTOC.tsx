import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { List, ChevronDown, ChevronUp, X } from 'lucide-react';
import { smoothScrollTo } from '../../utils/animations';

interface TOCItem {
  id: string;
  title: string;
  level: number;
}

interface EnhancedTOCProps {
  content: string;
  className?: string;
}

export function EnhancedTOC({ content, className = '' }: EnhancedTOCProps) {
  const [tocItems, setTocItems] = useState<TOCItem[]>([]);
  const [activeId, setActiveId] = useState<string>('');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      setIsCollapsed(mobile);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Extract headings from content
  useEffect(() => {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm;
    const items: TOCItem[] = [];
    let match;

    while ((match = headingRegex.exec(content)) !== null) {
      const level = match[1].length;
      const title = match[2].trim();
      const id = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
      
      items.push({ id, title, level });
    }

    setTocItems(items);
    setIsVisible(items.length > 0);
  }, [content]);

  // Track scroll position and update active heading
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min((scrollTop / docHeight) * 100, 100);
      setReadingProgress(progress);

      const headings = tocItems.map(item => document.getElementById(item.id)).filter(Boolean);
      
      if (headings.length === 0) return;

      const scrollPosition = window.scrollY + 100;
      
      for (let i = headings.length - 1; i >= 0; i--) {
        const heading = headings[i];
        if (heading && heading.offsetTop <= scrollPosition) {
          setActiveId(heading.id);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [tocItems]);

  const scrollToHeading = (id: string) => {
    smoothScrollTo(id);
    
    if (isMobile) {
      setIsCollapsed(true);
    }
  };

  if (!isVisible) return null;

  return (
    <div className={`${isMobile ? 'fixed bottom-4 right-4 z-40' : 'sticky top-24'} ${className}`}>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className={`bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark overflow-hidden ${
          isMobile ? 'max-w-xs' : 'w-64'
        }`}
      >
        {/* Reading Progress Bar */}
        <div className="h-1 bg-neutral-200 dark:bg-neutral-700">
          <motion.div
            className="h-full bg-primary-600"
            style={{ width: `${readingProgress}%` }}
            transition={{ duration: 0.1 }}
          />
        </div>

        {/* Header */}
        <motion.button
          onClick={() => setIsCollapsed(!isCollapsed)}
          whileHover={{ backgroundColor: 'rgba(0, 0, 0, 0.05)' }}
          whileTap={{ scale: 0.98 }}
          className="w-full flex items-center justify-between p-4 text-left transition-colors"
          aria-expanded={!isCollapsed}
          aria-controls="toc-content"
        >
          <div className="flex items-center">
            <List className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3" />
            <h3 className="font-semibold text-neutral-900 dark:text-neutral-100">
              {isMobile ? 'Contents' : 'Table of Contents'}
            </h3>
          </div>
          <motion.div
            animate={{ rotate: isCollapsed ? 0 : 180 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-neutral-500" />
          </motion.div>
        </motion.button>

        {/* Content */}
        <AnimatePresence>
          {!isCollapsed && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <nav className="p-4 pt-0 max-h-96 overflow-y-auto">
                <motion.ul 
                  className="space-y-1"
                  variants={{
                    animate: {
                      transition: {
                        staggerChildren: 0.05
                      }
                    }
                  }}
                  initial="initial"
                  animate="animate"
                >
                  {tocItems.map((item, index) => (
                    <motion.li
                      key={item.id}
                      variants={{
                        initial: { opacity: 0, x: -10 },
                        animate: { opacity: 1, x: 0 }
                      }}
                    >
                      <motion.button
                        onClick={() => scrollToHeading(item.id)}
                        whileHover={{ x: 4 }}
                        whileTap={{ scale: 0.98 }}
                        className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all relative ${
                          activeId === item.id
                            ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 font-medium'
                            : 'text-neutral-600 dark:text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-700'
                        }`}
                        style={{ paddingLeft: `${(item.level - 1) * 12 + 12}px` }}
                      >
                        <span className="line-clamp-2">{item.title}</span>
                        
                        {/* Active indicator */}
                        {activeId === item.id && (
                          <motion.div
                            layoutId="activeIndicator"
                            className="absolute left-1 top-1/2 w-1 h-4 bg-primary-600 rounded-full"
                            style={{ transform: 'translateY(-50%)' }}
                            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                          />
                        )}
                      </motion.button>
                    </motion.li>
                  ))}
                </motion.ul>
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
}