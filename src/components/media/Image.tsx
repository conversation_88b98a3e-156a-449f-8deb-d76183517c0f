import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface ImageProps {
  src: string;
  alt: string;
  className?: string;
  sizes?: string;
  priority?: boolean;
  placeholder?: string;
  onLoad?: () => void;
  onError?: () => void;
}

export function Image({
  src,
  alt,
  className = '',
  sizes,
  priority = false,
  placeholder,
  onLoad,
  onError,
}: ImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Generate srcset for responsive images (using Pexels URL pattern)
  const generateSrcSet = (baseSrc: string) => {
    if (!baseSrc.includes('pexels.com')) {
      return undefined;
    }

    const baseUrl = baseSrc.split('?')[0];
    const params = new URLSearchParams(baseSrc.split('?')[1] || '');
    
    const widths = [400, 800, 1200, 1600];
    return widths
      .map(width => {
        const newParams = new URLSearchParams(params);
        newParams.set('w', width.toString());
        return `${baseUrl}?${newParams.toString()} ${width}w`;
      })
      .join(', ');
  };

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  const srcSet = generateSrcSet(src);

  if (hasError) {
    return (
      <div className={`bg-neutral-200 dark:bg-neutral-700 flex items-center justify-center ${className}`}>
        <div className="text-center p-4">
          <div className="w-12 h-12 bg-neutral-300 dark:bg-neutral-600 rounded-lg mx-auto mb-2"></div>
          <p className="text-sm text-neutral-500 dark:text-neutral-400">
            Image failed to load
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Placeholder/Blur backdrop */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-neutral-200 dark:bg-neutral-700">
          {placeholder && (
            <img
              src={placeholder}
              alt=""
              className="w-full h-full object-cover blur-sm scale-110"
              aria-hidden="true"
            />
          )}
          
          {/* Loading animation */}
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                opacity: [0.5, 0.8, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="w-8 h-8 bg-neutral-300 dark:bg-neutral-600 rounded-lg"
            />
          </div>
        </div>
      )}

      {/* Main image */}
      <motion.img
        src={src}
        srcSet={srcSet}
        sizes={sizes || "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}
        alt={alt}
        onLoad={handleLoad}
        onError={handleError}
        loading={priority ? "eager" : "lazy"}
        initial={{ opacity: 0 }}
        animate={{ opacity: isLoaded ? 1 : 0 }}
        transition={{ duration: 0.3 }}
        className={`w-full h-full object-cover ${isLoaded ? 'relative' : 'absolute inset-0'}`}
      />
    </div>
  );
}