import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ErrorBoundary } from './components/system/ErrorBoundary';
import { SkipToContentLink } from './components/layout/SkipToContentLink';
import { Header } from './components/layout/Header';
import { MobileNavDrawer } from './components/layout/MobileNavDrawer';
import { EnhancedSearchOverlay } from './components/layout/EnhancedSearchOverlay';
import { Footer } from './components/layout/Footer';
import { ToastContainer } from './components/system/Toast';
import { EnhancedHomePage } from './pages/EnhancedHomePage';
import { CategoryPage } from './pages/CategoryPage';
import { CategoriesPage } from './pages/CategoriesPage';
import { ArticlePage } from './pages/ArticlePage';
import { AboutPage } from './pages/AboutPage';
import { HowWeTestPage } from './pages/HowWeTestPage';
import { NotFoundPage } from './components/system/NotFoundPage';
import { useToast } from './hooks/useToast';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
    },
  },
});

function AppContent() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { toasts, removeToast } = useToast();

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <SkipToContentLink />
      
      <Header
        onSearchOpen={() => setIsSearchOpen(true)}
        onMobileMenuOpen={() => setIsMobileMenuOpen(true)}
      />
      
      <MobileNavDrawer
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />
      
      <EnhancedSearchOverlay
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
      />

      <main>
        <Routes>
          <Route path="/" element={<EnhancedHomePage />} />
          <Route path="/categories" element={<CategoriesPage />} />
          <Route path="/category/:slug" element={<CategoryPageWrapper />} />
          <Route path="/articles/:slug" element={<ArticlePageWrapper />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/how-we-test" element={<HowWeTestPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </main>

      <Footer />
      
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </div>
  );
}

function CategoryPageWrapper() {
  const slug = window.location.pathname.split('/').pop() || '';
  return <CategoryPage categorySlug={slug} />;
}

function ArticlePageWrapper() {
  const slug = window.location.pathname.split('/').pop() || '';
  return <ArticlePage articleSlug={slug} />;
}

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Router>
          <AppContent />
        </Router>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;