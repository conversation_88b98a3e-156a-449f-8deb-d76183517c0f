@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple-inspired base styles */
@layer base {
  html {
    scroll-behavior: smooth;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Inter', 'SF Pro Display', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    transition: var(--theme-transition, background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), color 0.3s cubic-bezier(0.4, 0, 0.2, 1));
  }

  /* Prevent theme flicker - Default to light mode */
  html {
    background-color: #ffffff;
    color: #171717;
  }

  html.dark {
    background-color: #0a0a0a;
    color: #fafafa;
  }

  /* Apple-style focus rings */
  *:focus-visible {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
    border-radius: 8px;
  }

  /* Smooth theme transitions */
  * {
    transition-property: background-color, border-color, color, fill, stroke, box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
  }

  /* Improved button interactions */
  button, a {
    -webkit-tap-highlight-color: transparent;
  }

  /* Performance optimizations */
  img, video {
    content-visibility: auto;
  }

  /* Reduce motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }
}

/* Print Styles */
@media print {
  nav,
  .no-print,
  button,
  .sticky,
  .fixed {
    display: none !important;
  }

  body {
    font-family: Georgia, 'Times New Roman', serif !important;
    font-size: 12pt !important;
    line-height: 1.6 !important;
    color: #000 !important;
    background: #fff !important;
  }

  article {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: Georgia, 'Times New Roman', serif !important;
    color: #000 !important;
    page-break-after: avoid;
    margin-top: 24pt;
    margin-bottom: 12pt;
  }

  h1 { font-size: 24pt !important; }
  h2 { font-size: 20pt !important; }
  h3 { font-size: 16pt !important; }

  p {
    margin-bottom: 12pt;
    orphans: 3;
    widows: 3;
  }

  img {
    max-width: 100% !important;
    height: auto !important;
    page-break-inside: avoid;
  }

  blockquote {
    border-left: 3pt solid #ccc;
    padding-left: 12pt;
    margin: 12pt 0;
    font-style: italic;
  }

  a {
    color: #000 !important;
    text-decoration: underline !important;
  }

  a::after {
    content: " (" attr(href) ")";
    font-size: 10pt;
    color: #666;
  }

  .page-break {
    page-break-before: always;
  }
}

/* Custom scrollbar - Apple style */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Hide scrollbar for specific elements */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Apple-style selection */
::selection {
  background-color: theme('colors.primary.100');
  color: theme('colors.primary.900');
}

.dark ::selection {
  background-color: theme('colors.primary.800');
  color: theme('colors.primary.100');
}

/* Typography improvements */
.prose {
  --tw-prose-body: theme('colors.neutral.700');
  --tw-prose-headings: theme('colors.neutral.900');
  --tw-prose-lead: theme('colors.neutral.600');
  --tw-prose-links: theme('colors.primary.600');
  --tw-prose-bold: theme('colors.neutral.900');
  --tw-prose-counters: theme('colors.neutral.500');
  --tw-prose-bullets: theme('colors.neutral.300');
  --tw-prose-hr: theme('colors.neutral.200');
  --tw-prose-quotes: theme('colors.neutral.900');
  --tw-prose-quote-borders: theme('colors.neutral.200');
  --tw-prose-captions: theme('colors.neutral.500');
  --tw-prose-code: theme('colors.neutral.900');
  --tw-prose-pre-code: theme('colors.neutral.200');
  --tw-prose-pre-bg: theme('colors.neutral.800');
  --tw-prose-th-borders: theme('colors.neutral.300');
  --tw-prose-td-borders: theme('colors.neutral.200');
}

.dark .prose {
  --tw-prose-body: theme('colors.neutral.300');
  --tw-prose-headings: theme('colors.neutral.100');
  --tw-prose-lead: theme('colors.neutral.400');
  --tw-prose-links: theme('colors.primary.400');
  --tw-prose-bold: theme('colors.neutral.100');
  --tw-prose-counters: theme('colors.neutral.400');
  --tw-prose-bullets: theme('colors.neutral.600');
  --tw-prose-hr: theme('colors.neutral.700');
  --tw-prose-quotes: theme('colors.neutral.100');
  --tw-prose-quote-borders: theme('colors.neutral.700');
  --tw-prose-captions: theme('colors.neutral.400');
  --tw-prose-code: theme('colors.neutral.100');
  --tw-prose-pre-code: theme('colors.neutral.300');
  --tw-prose-pre-bg: theme('colors.neutral.900');
  --tw-prose-th-borders: theme('colors.neutral.600');
  --tw-prose-td-borders: theme('colors.neutral.700');
}

/* Apple-style button animations */
@keyframes button-press {
  0% { transform: scale(1); }
  50% { transform: scale(0.98); }
  100% { transform: scale(1); }
}

.btn-press:active {
  animation: button-press 0.1s ease-out;
}

/* Loading states */
@keyframes pulse-apple {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.animate-pulse-apple {
  animation: pulse-apple 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Focus improvements for better accessibility */
.focus-ring {
  @apply focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 focus-visible:outline-none;
}

.focus-ring-inset {
  @apply focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-inset focus-visible:outline-none;
}

/* Apple-style hero blocks */
.hero-block {
  background: linear-gradient(135deg, 
    theme('colors.primary.50') 0%, 
    theme('colors.white') 50%, 
    theme('colors.secondary.50') 100%);
}

.dark .hero-block {
  background: linear-gradient(135deg, 
    theme('colors.primary.950/20') 0%, 
    theme('colors.neutral.900') 50%, 
    theme('colors.secondary.950/20') 100%);
}

/* Enhanced card hover effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .card-hover:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Improved text rendering */
.text-rendering-optimized {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Content visibility for performance */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}