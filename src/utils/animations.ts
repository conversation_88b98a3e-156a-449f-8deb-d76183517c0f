import { Variants } from 'framer-motion';

// Centralized animation configurations for consistency
export const animations = {
  // Page transitions
  pageTransition: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.4, ease: [0.4, 0, 0.2, 1] }
  },

  // Staggered children animations
  staggerContainer: {
    animate: {
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  } as Variants,

  staggerItem: {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5, ease: [0.4, 0, 0.2, 1] }
    }
  } as Variants,

  // Card animations
  cardHover: {
    scale: 1.03,
    y: -4,
    transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] }
  },

  cardTap: {
    scale: 0.97,
    transition: { duration: 0.1 }
  },

  // Button animations
  buttonHover: {
    y: -1,
    scale: 1.02,
    transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] }
  },

  buttonTap: {
    scale: 0.97,
    transition: { duration: 0.1 }
  },

  // Verdict card entrance
  verdictEntrance: {
    initial: { opacity: 0, scale: 0.95, y: 30 },
    animate: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { duration: 0.6, ease: [0.4, 0, 0.2, 1] }
    }
  },

  // Pros/Cons list items
  prosConsItem: {
    initial: { opacity: 0, x: -10 },
    animate: (index: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.4,
        delay: index * 0.1,
        ease: [0.4, 0, 0.2, 1]
      }
    })
  },

  // Newsletter success
  successPulse: {
    scale: [1, 1.05, 1],
    transition: { duration: 0.6, ease: [0.4, 0, 0.2, 1] }
  },

  // Loading states
  shimmer: {
    x: ['-100%', '100%'],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: 'linear'
    }
  },

  // Scroll-triggered animations
  fadeInUp: {
    initial: { opacity: 0, y: 30 },
    whileInView: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6, ease: [0.4, 0, 0.2, 1] }
    },
    viewport: { once: true, margin: '-50px' }
  },

  // TOC animations
  tocHighlight: {
    backgroundColor: ['rgba(59, 130, 246, 0)', 'rgba(59, 130, 246, 0.1)', 'rgba(59, 130, 246, 0)'],
    transition: { duration: 0.8, ease: [0.4, 0, 0.2, 1] }
  },

  // Search overlay
  searchOverlay: {
    initial: { opacity: 0, scale: 0.95, y: -20 },
    animate: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] }
    },
    exit: { 
      opacity: 0, 
      scale: 0.95, 
      y: -20,
      transition: { duration: 0.15, ease: [0.4, 0, 0.2, 1] }
    }
  },

  // Mobile nav drawer
  drawerSlide: {
    initial: { x: '-100%' },
    animate: { 
      x: 0,
      transition: { type: 'spring', damping: 30, stiffness: 300 }
    },
    exit: { 
      x: '-100%',
      transition: { type: 'spring', damping: 30, stiffness: 300 }
    }
  }
};

// Easing curves
export const easings = {
  apple: [0.4, 0, 0.2, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
  smooth: [0.25, 0.46, 0.45, 0.94]
} as const;

// Utility function for smooth scroll
export const smoothScrollTo = (elementId: string, offset = 80) => {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.offsetTop - offset;
    window.scrollTo({
      top: elementPosition,
      behavior: 'smooth'
    });
  }
};

// Intersection Observer for scroll animations
export const createScrollObserver = (
  callback: (entries: IntersectionObserverEntry[]) => void,
  options?: IntersectionObserverInit
) => {
  const defaultOptions = {
    threshold: 0.1,
    rootMargin: '-50px 0px',
    ...options
  };

  return new IntersectionObserver(callback, defaultOptions);
};