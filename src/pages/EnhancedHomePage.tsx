import React from 'react';
import { motion } from 'framer-motion';
import { ArrowR<PERSON>, TrendingUp, Award, Users, Sparkles, Zap, Shield, Star } from 'lucide-react';
import { getFeaturedArticles, getArticlesByType } from '../data/articles';
import { AnimatedCard } from '../components/ui/AnimatedCard';
import { EnhancedNewsletterSignup } from '../components/content/EnhancedNewsletterSignup';
import { CategoryNavigation } from '../components/layout/CategoryNavigation';
import { FeaturedCategoriesSection } from '../components/sections/FeaturedCategoriesSection';
import { TrustSection } from '../components/sections/TrustSection';
import { AffiliateProductCard } from '../components/content/AffiliateProductCard';
import { SEO } from '../components/seo/SEO';
import { trackEvent, AnalyticsEvents } from '../lib/analytics';
import { animations } from '../utils/animations';
import { AnimatedButton } from '../components/ui/AnimatedButton';
import { PerformanceOptimizedImage } from '../components/ui/PerformanceOptimizedImage';

export function EnhancedHomePage() {
  const featuredArticles = getFeaturedArticles(3);
  const latestReviews = getArticlesByType('review').slice(0, 6);

  // Mock affiliate products for demonstration
  const featuredProducts = [
    {
      id: 'iphone-15-pro',
      name: 'iPhone 15 Pro',
      brand: 'Apple',
      image: 'https://images.pexels.com/photos/14666018/pexels-photo-14666018.jpeg?auto=compress&cs=tinysrgb&w=400',
      price: { current: 999, msrp: 1099, currency: 'USD' },
      rating: 4.5,
      specs: ['A17 Pro chip', '48MP camera system', 'Titanium design'],
      pros: ['Exceptional camera quality', 'Premium build', 'Great performance'],
      cons: ['Expensive', 'Limited storage options'],
      affiliateLinks: {
        amazon: 'https://amazon.com/iphone-15-pro',
        bestBuy: 'https://bestbuy.com/iphone-15-pro'
      }
    },
    {
      id: 'sony-wh-1000xm5',
      name: 'WH-1000XM5',
      brand: 'Sony',
      image: 'https://images.pexels.com/photos/3945681/pexels-photo-3945681.jpeg?auto=compress&cs=tinysrgb&w=400',
      price: { current: 349, msrp: 399, currency: 'USD' },
      rating: 4.3,
      specs: ['30hr battery', 'Industry-leading ANC', 'Quick charge'],
      pros: ['Excellent noise cancellation', 'Great sound quality', 'Comfortable'],
      cons: ['Not foldable', 'Premium pricing'],
      affiliateLinks: {
        amazon: 'https://amazon.com/sony-wh-1000xm5',
        bestBuy: 'https://bestbuy.com/sony-wh-1000xm5'
      }
    },
    {
      id: 'macbook-pro-m3',
      name: 'MacBook Pro M3',
      brand: 'Apple',
      image: 'https://images.pexels.com/photos/205421/pexels-photo-205421.jpeg?auto=compress&cs=tinysrgb&w=400',
      price: { current: 1599, msrp: 1699, currency: 'USD' },
      rating: 4.7,
      specs: ['M3 chip', '18hr battery', 'Liquid Retina XDR'],
      pros: ['Incredible performance', 'Amazing display', 'Long battery life'],
      cons: ['Very expensive', 'Limited ports'],
      affiliateLinks: {
        amazon: 'https://amazon.com/macbook-pro-m3',
        bestBuy: 'https://bestbuy.com/macbook-pro-m3'
      }
    }
  ];

  const handleArticleClick = (articleId: string, title: string, location: string) => {
    trackEvent(AnalyticsEvents.ARTICLE_VIEW, {
      article_id: articleId,
      article_title: title,
      location,
    });
  };

  const handleCTAClick = (cta: string, location: string) => {
    trackEvent(AnalyticsEvents.CTA_CLICK, {
      cta,
      location,
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <SEO />

      {/* Hero Section - Apple-inspired */}
      <section className="relative overflow-hidden bg-gradient-to-br from-white via-neutral-50/50 to-primary-50/30 dark:from-neutral-900 dark:via-neutral-900 dark:to-primary-950/30">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]" />
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="text-center">
            <motion.div
              variants={animations.staggerContainer}
              initial="initial"
              animate="animate"
              className="mb-8"
            >
              <motion.div
                variants={animations.staggerItem}
                className="inline-flex items-center px-4 py-2 bg-primary-100/50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium mb-6 border border-primary-200/50 dark:border-primary-800/50"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Trusted by 2M+ tech enthusiasts
              </motion.div>
              
              <motion.h1
                variants={animations.staggerItem}
                className="text-4xl md:text-6xl lg:text-7xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight"
              >
                <span className="bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-600 bg-clip-text text-transparent">
                  Expert Tech Reviews
                </span>
                <br />
                <span className="text-neutral-900 dark:text-neutral-100">
                  You Can Trust
                </span>
              </motion.h1>
              
              <motion.p
                variants={animations.staggerItem}
                className="text-xl md:text-2xl text-neutral-600 dark:text-neutral-400 max-w-4xl mx-auto leading-relaxed font-light"
              >
                In-depth analysis, unbiased opinions, and honest recommendations to help you make informed tech decisions with confidence.
              </motion.p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center mb-16"
            >
              <AnimatedButton
                href="/categories"
                onClick={() => handleCTAClick('Browse Categories', 'Hero')}
                variant="primary"
                size="lg"
              >
                Browse Categories
                <ArrowRight className="ml-2 w-5 h-5" />
              </AnimatedButton>
              
              <AnimatedButton
                href="/about"
                onClick={() => handleCTAClick('Learn More', 'Hero')}
                variant="secondary"
                size="lg"
              >
                Learn More
              </AnimatedButton>
            </motion.div>

            {/* Stats - Apple-style */}
            <motion.div
              variants={animations.staggerContainer}
              initial="initial"
              animate="animate"
              className="grid grid-cols-3 gap-8 max-w-2xl mx-auto"
            >
              {[
                { value: '500+', label: 'Expert Reviews' },
                { value: '2M+', label: 'Monthly Readers' },
                { value: '5 Years', label: 'Industry Experience' }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  variants={animations.staggerItem}
                  className="text-center"
                >
                  <div className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
                    {stat.value}
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400 font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Category Navigation */}
      <CategoryNavigation />

      <main id="main-content">
        {/* Featured Article Hero */}
        {featuredArticles.length > 0 && (
          <section className="py-12 lg:py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="relative"
              >
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                  <motion.div
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="order-2 lg:order-1"
                  >
                    <div className="inline-flex items-center px-3 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium mb-4">
                      <Star className="w-4 h-4 mr-2" />
                      Featured Review
                    </div>
                    
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 tracking-tight leading-tight">
                      {featuredArticles[0].title}
                    </h2>
                    
                    <p className="text-lg text-neutral-600 dark:text-neutral-400 mb-6 leading-relaxed">
                      {featuredArticles[0].excerpt}
                    </p>
                    
                    <div className="flex items-center space-x-6 text-sm text-neutral-500 dark:text-neutral-400 mb-8">
                      <span>By {featuredArticles[0].meta.authors[0]?.name}</span>
                      <span>{formatDate(featuredArticles[0].published_date)}</span>
                      <span>{featuredArticles[0].reading_time} min read</span>
                      {featuredArticles[0].review_data && (
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-warning-400 mr-1" />
                          <span className="font-medium text-neutral-700 dark:text-neutral-300">
                            {featuredArticles[0].review_data.rating.overall.toFixed(1)}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <AnimatedButton
                      href={`/articles/${featuredArticles[0].slug}`}
                      onClick={() => handleArticleClick(featuredArticles[0].id, featuredArticles[0].title, 'Featured Hero')}
                      variant="primary"
                    >
                      Read Full Review
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </AnimatedButton>
                  </motion.div>
                  
                  <motion.div
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="order-1 lg:order-2"
                  >
                    <div className="aspect-[4/3] rounded-2xl overflow-hidden shadow-apple dark:shadow-apple-dark">
                      <PerformanceOptimizedImage
                        src={featuredArticles[0].featured_image!}
                        alt={featuredArticles[0].title}
                        className="w-full h-full"
                        priority
                      />
                    </div>
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </section>
        )}

        {/* Editor's Choice Products */}
        <section className="py-20 lg:py-32 bg-neutral-50/30 dark:bg-neutral-800/10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight">
                Editor's Choice
              </h2>
              <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto leading-relaxed">
                Our top-rated products across all categories, tested and recommended by our expert team.
              </p>
            </motion.div>

            <motion.div
              variants={animations.staggerContainer}
              initial="initial"
              whileInView="animate"
              viewport={{ once: true }}
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {featuredProducts.map((product, index) => (
                <motion.div
                  key={product.id}
                  variants={animations.staggerItem}
                >
                  <AffiliateProductCard
                    product={product}
                    variant="featured"
                  />
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Latest Reviews */}
        <section className="py-20 lg:py-32">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between mb-16">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 tracking-tight">
                  Latest Reviews
                </h2>
                <p className="text-xl text-neutral-600 dark:text-neutral-400">
                  Fresh insights on the newest tech releases
                </p>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <AnimatedButton
                  href="/categories"
                  onClick={() => handleCTAClick('View All Reviews', 'Latest Reviews')}
                  variant="primary"
                  className="hidden sm:inline-flex"
                >
                  View All
                  <ArrowRight className="ml-2 w-4 h-4" />
                </AnimatedButton>
              </motion.div>
            </div>

            {/* Mixed grid layout */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
              {/* Large featured article */}
              {latestReviews[0] && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="lg:col-span-8"
                >
                  <AnimatedCard
                    href={`/articles/${latestReviews[0].slug}`}
                    onClick={() => handleArticleClick(latestReviews[0].id, latestReviews[0].title, 'Latest Reviews')}
                    variant="hover"
                    className="bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark overflow-hidden p-8"
                  >
                    <div className="aspect-video rounded-xl overflow-hidden mb-6">
                      <PerformanceOptimizedImage
                        src={latestReviews[0].featured_image!}
                        alt={latestReviews[0].title}
                        className="w-full h-full"
                      />
                    </div>
                    <div className="inline-flex px-3 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium mb-4">
                      {latestReviews[0].type}
                    </div>
                    <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3 line-clamp-2">
                      {latestReviews[0].title}
                    </h3>
                    <p className="text-neutral-600 dark:text-neutral-400 line-clamp-3 mb-4">
                      {latestReviews[0].excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-neutral-500 dark:text-neutral-400">
                        <span>{formatDate(latestReviews[0].published_date)}</span>
                        <span>{latestReviews[0].reading_time} min read</span>
                      </div>
                      {latestReviews[0].review_data && (
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-warning-400 mr-1" />
                          <span className="font-medium text-neutral-700 dark:text-neutral-300">
                            {latestReviews[0].review_data.rating.overall.toFixed(1)}
                          </span>
                        </div>
                      )}
                    </div>
                  </AnimatedCard>
                </motion.div>
              )}

              {/* Sidebar articles */}
              <div className="lg:col-span-4 space-y-6">
                {latestReviews.slice(1, 4).map((article, index) => (
                  <AnimatedCard
                    key={article.id}
                    href={`/articles/${article.slug}`}
                    onClick={() => handleArticleClick(article.id, article.title, 'Latest Reviews Sidebar')}
                    variant="hover"
                    delay={0.1 + index * 0.1}
                    className="bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark overflow-hidden p-4"
                  >
                    <div className="flex space-x-4">
                      <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                        <PerformanceOptimizedImage
                          src={article.featured_image!}
                          alt={article.title}
                          className="w-full h-full"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="inline-flex px-2 py-0.5 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-xs font-medium mb-2">
                          {article.type}
                        </div>
                        <h3 className="font-bold text-neutral-900 dark:text-neutral-100 mb-2 line-clamp-2 text-sm">
                          {article.title}
                        </h3>
                        <div className="flex items-center justify-between text-xs text-neutral-500 dark:text-neutral-400">
                          <span>{formatDate(article.published_date)}</span>
                          {article.review_data && (
                            <div className="flex items-center">
                              <Star className="w-3 h-3 text-warning-400 mr-1" />
                              <span>{article.review_data.rating.overall.toFixed(1)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </AnimatedCard>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Featured Categories Section */}
        <FeaturedCategoriesSection />

        {/* Trust Section */}
        <TrustSection />

        {/* Newsletter Section */}
        <section className="py-20 lg:py-32 bg-gradient-to-br from-primary-50/50 via-white to-secondary-50/30 dark:from-primary-950/20 dark:via-neutral-900 dark:to-secondary-950/20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <EnhancedNewsletterSignup variant="inline" />
            </motion.div>
          </div>
        </section>
      </main>
    </div>
  );
}