import React from 'react';
import { motion } from 'framer-motion';
import { Award, Users, Shield, Target, Heart, Zap } from 'lucide-react';
import { SEO } from '../components/seo/SEO';
import { authors } from '../data/articles';

export function AboutPage() {
  const stats = [
    { label: 'Expert Reviews', value: '500+' },
    { label: 'Monthly Readers', value: '2M+' },
    { label: 'Years Experience', value: '5+' },
    { label: 'Products Tested', value: '1000+' },
  ];

  const values = [
    {
      icon: Shield,
      title: 'Unbiased Reviews',
      description: 'We maintain complete editorial independence and never let sponsorships influence our honest opinions.',
    },
    {
      icon: Target,
      title: 'Thorough Testing',
      description: 'Every product undergoes rigorous real-world testing to ensure our recommendations are reliable.',
    },
    {
      icon: Heart,
      title: 'Community First',
      description: 'We listen to our readers and create content that addresses real needs and questions.',
    },
    {
      icon: Zap,
      title: 'Latest Technology',
      description: 'We stay ahead of tech trends to bring you insights on the newest innovations and releases.',
    },
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <SEO
        title="About Apex Tech - Expert Tech Reviews & Analysis"
        description="Learn about Apex Tech's mission to provide unbiased, thorough tech reviews and buying guides. Meet our expert team and discover our testing methodology."
        keywords={['about apex tech', 'tech review team', 'editorial policy', 'testing methodology']}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight">
            About Apex Tech
          </h1>
          <p className="text-xl md:text-2xl text-neutral-600 dark:text-neutral-400 max-w-4xl mx-auto leading-relaxed">
            We're passionate about technology and committed to helping you make informed decisions through honest, thorough, and unbiased reviews.
          </p>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
        >
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                {stat.value}
              </div>
              <div className="text-sm text-neutral-600 dark:text-neutral-400 font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </motion.div>

        {/* Mission */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-20"
        >
          <div className="bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 rounded-3xl p-8 lg:p-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
              Our Mission
            </h2>
            <p className="text-lg text-neutral-700 dark:text-neutral-300 leading-relaxed mb-6">
              In a world flooded with tech products and marketing claims, we believe you deserve honest, comprehensive reviews that cut through the noise. Our mission is to be your trusted guide in the ever-evolving world of technology.
            </p>
            <p className="text-lg text-neutral-700 dark:text-neutral-300 leading-relaxed">
              We test every product thoroughly, consider real-world use cases, and provide recommendations based on actual performance—not hype or sponsorship deals.
            </p>
          </div>
        </motion.section>

        {/* Values */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-20"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-12 text-center">
            Our Values
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                className="bg-white dark:bg-neutral-800 rounded-2xl p-6 border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark"
              >
                <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center mb-4">
                  <value.icon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                </div>
                <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-100 mb-3">
                  {value.title}
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Team */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-20"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-12 text-center">
            Meet Our Team
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {authors.map((author, index) => (
              <motion.div
                key={author.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                className="text-center"
              >
                <div className="w-32 h-32 mx-auto mb-6 rounded-full overflow-hidden">
                  <img
                    src={author.avatar}
                    alt={author.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
                  {author.name}
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed">
                  {author.bio}
                </p>
                {author.social && (
                  <div className="flex justify-center space-x-4 mt-4">
                    {author.social.twitter && (
                      <a
                        href={`https://twitter.com/${author.social.twitter.replace('@', '')}`}
                        className="text-neutral-500 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Twitter
                      </a>
                    )}
                    {author.social.linkedin && (
                      <a
                        href={`https://linkedin.com/in/${author.social.linkedin}`}
                        className="text-neutral-500 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        LinkedIn
                      </a>
                    )}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Contact */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="text-center"
        >
          <div className="bg-neutral-50 dark:bg-neutral-800 rounded-3xl p-8 lg:p-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
              Get in Touch
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 mb-8 max-w-2xl mx-auto">
              Have questions about our reviews? Want to suggest a product for testing? We'd love to hear from you.
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-xl transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
            >
              Contact Us
            </a>
          </div>
        </motion.section>
      </div>
    </div>
  );
}