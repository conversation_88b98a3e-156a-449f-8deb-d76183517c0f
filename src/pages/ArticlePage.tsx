import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Clock, Calendar, User, Share2, ArrowLeft, Bookmark, ExternalLink } from 'lucide-react';
import { getArticleBySlug, getFeaturedArticles } from '../data/articles';
import { getCategoryHierarchy } from '../data/categories';
import { SEO } from '../components/seo/SEO';
import { Breadcrumbs } from '../components/navigation/Breadcrumbs';
import { StickyTOC } from '../components/navigation/StickyTOC';
import { VerdictCard } from '../components/content/VerdictCard';
import { StickyVerdictCard } from '../components/content/StickyVerdictCard';
import { AffiliateDisclosure } from '../components/content/AffiliateDisclosure';
import { NewsletterSignup } from '../components/content/NewsletterSignup';
import { ProsConsBlock } from '../components/content/ProsConsBlock';
import { AppleCard } from '../components/ui/AppleCard';
import { Image } from '../components/media/Image';
import { EmptyState } from '../components/system/EmptyState';
import { LoadingSkeleton } from '../components/system/LoadingSkeleton';
import { trackEvent, AnalyticsEvents } from '../lib/analytics';

interface ArticlePageProps {
  articleSlug: string;
}

export function ArticlePage({ articleSlug }: ArticlePageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [readingProgress, setReadingProgress] = useState(0);
  
  const article = getArticleBySlug(articleSlug);
  const relatedArticles = getFeaturedArticles(3).filter(a => a.id !== article?.id);

  useEffect(() => {
    // Simulate loading time for better UX
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!article) return;

    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = Math.min((scrollTop / docHeight) * 100, 100);
      setReadingProgress(progress);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [article]);

  useEffect(() => {
    if (article) {
      // Track article view
      trackEvent(AnalyticsEvents.ARTICLE_VIEW, {
        article_id: article.id,
        article_title: article.title,
        article_type: article.type,
        category: article.category,
        reading_time: article.reading_time,
      });
    }
  }, [article]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white dark:bg-neutral-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <LoadingSkeleton variant="article" />
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-white dark:bg-neutral-900 flex items-center justify-center">
        <EmptyState
          variant="error"
          title="Article Not Found"
          description="The article you're looking for doesn't exist or has been moved."
          action={{
            label: 'Browse Articles',
            onClick: () => window.location.href = '/'
          }}
        />
      </div>
    );
  }

  const categoryHierarchy = getCategoryHierarchy(article.category.toLowerCase());
  const breadcrumbItems = [
    ...categoryHierarchy.map(cat => ({
      label: cat.name,
      href: `/category/${cat.slug}`
    })),
    { label: article.title }
  ];

  const handleShare = async () => {
    const shareData = {
      title: article.title,
      text: article.excerpt,
      url: window.location.href,
    };

    try {
      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        await navigator.clipboard.writeText(window.location.href);
      }
      
      trackEvent(AnalyticsEvents.CTA_CLICK, {
        location: 'ArticlePage',
        component_id: 'share-button',
        article_id: article.id,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const renderContent = (content: string) => {
    return content
      .split('\n')
      .map((line, index) => {
        if (line.startsWith('# ')) {
          const text = line.substring(2);
          const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
          return <h1 key={index} id={id} className="text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 mt-12 first:mt-0 scroll-mt-24">{text}</h1>;
        }
        if (line.startsWith('## ')) {
          const text = line.substring(3);
          const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
          return <h2 key={index} id={id} className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 mt-10 scroll-mt-24">{text}</h2>;
        }
        if (line.startsWith('### ')) {
          const text = line.substring(4);
          const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
          return <h3 key={index} id={id} className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3 mt-8 scroll-mt-24">{text}</h3>;
        }
        
        if (line.startsWith('- ')) {
          return <li key={index} className="text-neutral-700 dark:text-neutral-300 mb-2">{line.substring(2)}</li>;
        }
        
        if (line.trim() && !line.startsWith('#')) {
          return <p key={index} className="text-neutral-700 dark:text-neutral-300 leading-relaxed mb-4">{line}</p>;
        }
        
        return null;
      })
      .filter(Boolean);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <SEO article={article} type="article" />
      
      {/* Reading Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-neutral-200 dark:bg-neutral-800 z-50">
        <motion.div
          className="h-full bg-primary-600"
          style={{ width: `${readingProgress}%` }}
          transition={{ duration: 0.1 }}
        />
      </div>

      {/* Sticky Verdict Card */}
      {article.review_data && (
        <StickyVerdictCard reviewData={article.review_data} />
      )}

      {/* Sticky TOC */}
      <StickyTOC content={article.content} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button & Breadcrumbs */}
        <div className="flex items-center justify-between mb-8">
          <nav aria-label="Breadcrumb">
            <Breadcrumbs items={breadcrumbItems} />
          </nav>
          
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </button>
        </div>

        <div className="grid lg:grid-cols-12 gap-8">
          <article className="lg:col-span-8">
            <motion.header
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <div className="mb-4">
                <span className="inline-flex px-3 py-1 text-sm font-medium bg-primary-100 dark:bg-primary-900/20 text-primary-800 dark:text-primary-400 rounded-full">
                  {article.type}
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight leading-tight">
                {article.title}
              </h1>

              <p className="text-xl text-neutral-600 dark:text-neutral-400 leading-relaxed mb-8">
                {article.excerpt}
              </p>

              <div className="flex flex-wrap items-center gap-6 text-sm text-neutral-500 dark:text-neutral-400 mb-8">
                <div className="flex items-center">
                  <User className="w-4 h-4 mr-2" />
                  <span>By {article.meta.authors[0]?.name}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2" />
                  <time dateTime={article.published_date}>
                    {formatDate(article.published_date)}
                  </time>
                </div>
                {article.updated_date && article.updated_date !== article.published_date && (
                  <div className="flex items-center">
                    <span className="text-xs">Updated {formatDate(article.updated_date)}</span>
                  </div>
                )}
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2" />
                  <span>{article.reading_time} min read</span>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleShare}
                    className="flex items-center hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                  >
                    <Share2 className="w-4 h-4 mr-1" />
                    Share
                  </button>
                  <button className="flex items-center hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                    <Bookmark className="w-4 h-4 mr-1" />
                    Save
                  </button>
                </div>
              </div>

              {article.featured_image && (
                <div className="aspect-video rounded-2xl overflow-hidden mb-8 shadow-apple dark:shadow-apple-dark">
                  <Image
                    src={article.featured_image}
                    alt={article.title}
                    className="w-full h-full object-cover"
                    priority
                  />
                </div>
              )}
            </motion.header>

            {/* Author Bio */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="mb-8 p-6 bg-neutral-50 dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700"
            >
              <div className="flex items-start space-x-4">
                {article.meta.authors[0]?.avatar && (
                  <img
                    src={article.meta.authors[0].avatar}
                    alt={article.meta.authors[0].name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                )}
                <div className="flex-1">
                  <h3 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-1">
                    {article.meta.authors[0]?.name}
                  </h3>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed">
                    {article.meta.authors[0]?.bio}
                  </p>
                  {article.meta.authors[0]?.social && (
                    <div className="flex items-center space-x-4 mt-3">
                      {article.meta.authors[0].social.twitter && (
                        <a
                          href={`https://twitter.com/${article.meta.authors[0].social.twitter.replace('@', '')}`}
                          className="text-sm text-primary-600 dark:text-primary-400 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Twitter
                        </a>
                      )}
                      {article.meta.authors[0].social.linkedin && (
                        <a
                          href={`https://linkedin.com/in/${article.meta.authors[0].social.linkedin}`}
                          className="text-sm text-primary-600 dark:text-primary-400 hover:underline"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          LinkedIn
                        </a>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>

            {article.review_data?.affiliate_links && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="mb-8"
              >
                <AffiliateDisclosure variant="banner" />
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="prose prose-lg max-w-none prose-neutral dark:prose-invert"
            >
              {renderContent(article.content)}
            </motion.div>

            {/* Pros & Cons Block */}
            {article.review_data && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="mt-12"
              >
                <ProsConsBlock
                  pros={article.review_data.pros}
                  cons={article.review_data.cons}
                />
              </motion.div>
            )}

            {/* Article Footer */}
            <motion.footer
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="mt-16 pt-8 border-t border-neutral-200 dark:border-neutral-700"
            >
              <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex flex-wrap items-center gap-2">
                  <span className="text-sm text-neutral-500 dark:text-neutral-400">Tags:</span>
                  {article.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex px-3 py-1 text-xs font-medium bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                
                <div className="flex items-center space-x-4">
                  <button
                    onClick={handleShare}
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-lg transition-colors"
                  >
                    <Share2 className="w-4 h-4 mr-2" />
                    Share Article
                  </button>
                </div>
              </div>

              {article.review_data?.affiliate_links && (
                <div className="mt-8">
                  <AffiliateDisclosure variant="footer" />
                </div>
              )}
            </motion.footer>

            {/* Related Articles */}
            {relatedArticles.length > 0 && (
              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="mt-16"
              >
                <h2 className="text-3xl font-bold text-neutral-900 dark:text-neutral-100 mb-8">
                  Related Articles
                </h2>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {relatedArticles.map((relatedArticle) => (
                    <AppleCard
                      key={relatedArticle.id}
                      title={relatedArticle.title}
                      description={relatedArticle.excerpt}
                      image={relatedArticle.featured_image}
                      imageAlt={relatedArticle.title}
                      href={`/articles/${relatedArticle.slug}`}
                      badge={{
                        text: relatedArticle.type,
                        variant: 'primary'
                      }}
                      rating={relatedArticle.review_data?.rating.overall}
                      date={formatDate(relatedArticle.published_date)}
                      readTime={`${relatedArticle.reading_time} min`}
                      size="small"
                      variant="minimal"
                    />
                  ))}
                </div>
              </motion.section>
            )}

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 }}
              className="mt-16"
            >
              <NewsletterSignup variant="inline" />
            </motion.div>
          </article>

          <aside className="lg:col-span-4">
            <div className="sticky top-24 space-y-8">
              {article.review_data && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <VerdictCard reviewData={article.review_data} />
                </motion.div>
              )}
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}