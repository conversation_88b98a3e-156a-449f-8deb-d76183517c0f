import React from 'react';
import { motion } from 'framer-motion';
import { Grid3X3, TrendingUp } from 'lucide-react';
import { CategoryCard } from '../components/ui/CategoryCard';
import { SEO } from '../components/seo/SEO';
import categoriesData from '../data/categories.json';

export function CategoriesPage() {
  const allCategories = categoriesData.categories;
  const featuredCategories = allCategories.filter(category => category.featured);
  const otherCategories = allCategories.filter(category => !category.featured);

  const handleCategoryClick = (categorySlug: string, categoryName: string) => {
    console.log(`Category clicked: ${categoryName} (${categorySlug})`);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <SEO
        title="All Categories - Apex Tech"
        description="Browse all tech categories including smartphones, laptops, gaming, audio, and more. Find expert reviews and buying guides for every tech category."
        keywords={['tech categories', 'product categories', 'tech reviews', 'buying guides']}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center px-4 py-2 bg-primary-100/50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium mb-6 border border-primary-200/50 dark:border-primary-800/50">
            <Grid3X3 className="w-4 h-4 mr-2" />
            All Categories
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight">
            Explore Tech Categories
          </h1>
          <p className="text-xl md:text-2xl text-neutral-600 dark:text-neutral-400 max-w-4xl mx-auto leading-relaxed">
            Find expert reviews, buying guides, and in-depth analysis across all major tech categories.
          </p>
        </motion.div>

        {/* Featured Categories */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-20"
        >
          <div className="flex items-center mb-12">
            <TrendingUp className="w-6 h-6 text-primary-600 dark:text-primary-400 mr-3" />
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100">
              Most Popular
            </h2>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredCategories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
              >
                <CategoryCard
                  title={category.name}
                  description={category.description}
                  image={category.image}
                  href={`/category/${category.slug}`}
                  articleCount={category.articleCount}
                  onClick={() => handleCategoryClick(category.slug, category.name)}
                  variant="featured"
                />
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Other Categories */}
        {otherCategories.length > 0 && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-12">
              More Categories
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {otherCategories.map((category, index) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                >
                  <CategoryCard
                    title={category.name}
                    description={category.description}
                    image={category.image}
                    href={`/category/${category.slug}`}
                    articleCount={category.articleCount}
                    onClick={() => handleCategoryClick(category.slug, category.name)}
                    variant="default"
                  />
                </motion.div>
              ))}
            </div>
          </motion.section>
        )}
      </div>
    </div>
  );
}