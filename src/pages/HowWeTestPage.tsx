import React from 'react';
import { motion } from 'framer-motion';
import { 
  Microscope, 
  Clock, 
  Users, 
  Shield, 
  Target, 
  BarChart3,
  CheckCircle,
  Camera,
  Headphones,
  Monitor,
  Smartphone,
  Laptop
} from 'lucide-react';
import { SEO } from '../components/seo/SEO';

export function HowWeTestPage() {
  const testingPhases = [
    {
      icon: Target,
      title: 'Initial Assessment',
      description: 'We evaluate build quality, design, and first impressions during unboxing.',
      duration: '1-2 days',
    },
    {
      icon: Microscope,
      title: 'Technical Testing',
      description: 'Comprehensive performance benchmarks and feature analysis.',
      duration: '1-2 weeks',
    },
    {
      icon: Users,
      title: 'Real-World Usage',
      description: 'Extended daily use to understand practical performance and reliability.',
      duration: '2-4 weeks',
    },
    {
      icon: BarChart3,
      title: 'Data Analysis',
      description: 'Compilation and analysis of all test results and user feedback.',
      duration: '3-5 days',
    },
    {
      icon: CheckCircle,
      title: 'Review Writing',
      description: 'Crafting comprehensive reviews with clear recommendations.',
      duration: '2-3 days',
    },
  ];

  const categoryMethods = [
    {
      icon: Smartphone,
      category: 'Smartphones',
      tests: [
        'Camera quality in various lighting conditions',
        'Battery life with real-world usage patterns',
        'Performance benchmarks and gaming tests',
        'Display quality and color accuracy',
        'Build quality and durability testing',
      ],
    },
    {
      icon: Laptop,
      category: 'Laptops',
      tests: [
        'CPU and GPU performance benchmarks',
        'Battery life across different workloads',
        'Thermal management and fan noise',
        'Display quality and color gamut',
        'Keyboard and trackpad usability',
      ],
    },
    {
      icon: Headphones,
      category: 'Audio',
      tests: [
        'Frequency response measurements',
        'Noise cancellation effectiveness',
        'Comfort during extended use',
        'Build quality and durability',
        'Microphone quality for calls',
      ],
    },
    {
      icon: Monitor,
      category: 'Monitors',
      tests: [
        'Color accuracy and calibration',
        'Response time and input lag',
        'Brightness and contrast ratios',
        'Viewing angles and uniformity',
        'Gaming performance and features',
      ],
    },
  ];

  const principles = [
    {
      icon: Shield,
      title: 'Independence',
      description: 'We purchase products with our own funds or return review units immediately after testing.',
    },
    {
      icon: Clock,
      title: 'Time Investment',
      description: 'Every product gets weeks of testing, not just a few hours of initial impressions.',
    },
    {
      icon: Users,
      title: 'Real-World Focus',
      description: 'We test how products perform in actual daily use, not just controlled lab conditions.',
    },
    {
      icon: Target,
      title: 'Transparency',
      description: 'We clearly explain our testing methods and any limitations in our reviews.',
    },
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <SEO
        title="How We Test - Apex Tech Testing Methodology"
        description="Learn about Apex Tech's comprehensive testing methodology. Discover how we evaluate tech products through rigorous real-world testing and analysis."
        keywords={['testing methodology', 'product testing', 'review process', 'tech evaluation']}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight">
            How We Test
          </h1>
          <p className="text-xl md:text-2xl text-neutral-600 dark:text-neutral-400 max-w-4xl mx-auto leading-relaxed">
            Our rigorous testing methodology ensures every review is thorough, unbiased, and based on real-world performance.
          </p>
        </motion.div>

        {/* Testing Process */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-20"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-12 text-center">
            Our Testing Process
          </h2>
          <div className="space-y-8">
            {testingPhases.map((phase, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                className="flex items-center space-x-6 p-6 bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark"
              >
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-2xl flex items-center justify-center">
                    <phase.icon className="w-8 h-8 text-primary-600 dark:text-primary-400" />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-100">
                      {phase.title}
                    </h3>
                    <span className="text-sm text-primary-600 dark:text-primary-400 font-medium">
                      {phase.duration}
                    </span>
                  </div>
                  <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed">
                    {phase.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Category-Specific Testing */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-20"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-12 text-center">
            Category-Specific Testing
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {categoryMethods.map((category, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                className="bg-white dark:bg-neutral-800 rounded-2xl p-6 border border-neutral-200 dark:border-neutral-700 shadow-apple dark:shadow-apple-dark"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center mr-4">
                    <category.icon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                  </div>
                  <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-100">
                    {category.category}
                  </h3>
                </div>
                <ul className="space-y-3">
                  {category.tests.map((test, testIndex) => (
                    <li key={testIndex} className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-success-500 mt-0.5 mr-3 flex-shrink-0" />
                      <span className="text-neutral-600 dark:text-neutral-400">{test}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Testing Principles */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-20"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-12 text-center">
            Our Testing Principles
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {principles.map((principle, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                className="flex items-start space-x-4"
              >
                <div className="w-12 h-12 bg-primary-100 dark:bg-primary-900/20 rounded-xl flex items-center justify-center flex-shrink-0">
                  <principle.icon className="w-6 h-6 text-primary-600 dark:text-primary-400" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
                    {principle.title}
                  </h3>
                  <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed">
                    {principle.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Equipment & Tools */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mb-20"
        >
          <div className="bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 rounded-3xl p-8 lg:p-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
              Professional Equipment
            </h2>
            <p className="text-lg text-neutral-700 dark:text-neutral-300 leading-relaxed mb-6">
              We invest in professional-grade testing equipment to ensure accurate measurements and consistent results across all our reviews.
            </p>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-3">
                  Measurement Tools
                </h4>
                <ul className="space-y-2 text-neutral-600 dark:text-neutral-400">
                  <li>• Colorimeters for display testing</li>
                  <li>• Audio analyzers for frequency response</li>
                  <li>• Thermal cameras for heat analysis</li>
                  <li>• Precision scales and calipers</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-neutral-900 dark:text-neutral-100 mb-3">
                  Software & Benchmarks
                </h4>
                <ul className="space-y-2 text-neutral-600 dark:text-neutral-400">
                  <li>• Industry-standard benchmarking suites</li>
                  <li>• Custom testing scripts and automation</li>
                  <li>• Professional calibration software</li>
                  <li>• Data logging and analysis tools</li>
                </ul>
              </div>
            </div>
          </div>
        </motion.section>

        {/* Contact */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center"
        >
          <div className="bg-neutral-50 dark:bg-neutral-800 rounded-3xl p-8 lg:p-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-6">
              Questions About Our Testing?
            </h2>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 mb-8 max-w-2xl mx-auto">
              We're always happy to discuss our methodology or answer questions about specific tests.
            </p>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-xl transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
            >
              Contact Our Testing Team
            </a>
          </div>
        </motion.section>
      </div>
    </div>
  );
}