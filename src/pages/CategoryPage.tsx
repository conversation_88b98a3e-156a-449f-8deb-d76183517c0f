import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Filter, Grid, List, Star, ChevronDown, X } from 'lucide-react';
import { getCategoryBySlug } from '../data/categories';
import { getArticlesByCategory } from '../data/articles';
import { AppleCard } from '../components/ui/AppleCard';
import { SEO } from '../components/seo/SEO';
import { EmptyState } from '../components/system/EmptyState';

interface CategoryPageProps {
  categorySlug: string;
}

type SortOption = 'newest' | 'oldest' | 'rating' | 'title';
type ViewMode = 'grid' | 'list';
type FilterType = 'all' | 'review' | 'guide' | 'news' | 'comparison';

export function CategoryPage({ categorySlug }: CategoryPageProps) {
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [typeFilter, setTypeFilter] = useState<FilterType>('all');
  const [isFilterSticky, setIsFilterSticky] = useState(false);

  const category = getCategoryBySlug(categorySlug);
  const allArticles = category ? getArticlesByCategory(category.name) : [];

  // Filter articles by type
  const filteredArticles = allArticles.filter(article => 
    typeFilter === 'all' || article.type === typeFilter
  );

  // Sort articles
  const sortedArticles = [...filteredArticles].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.published_date).getTime() - new Date(a.published_date).getTime();
      case 'oldest':
        return new Date(a.published_date).getTime() - new Date(b.published_date).getTime();
      case 'rating':
        const aRating = a.review_data?.rating.overall || 0;
        const bRating = b.review_data?.rating.overall || 0;
        return bRating - aRating;
      case 'title':
        return a.title.localeCompare(b.title);
      default:
        return 0;
    }
  });

  // Handle sticky filters
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsFilterSticky(scrollPosition > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (!category) {
    return (
      <div className="min-h-screen bg-white dark:bg-neutral-900 flex items-center justify-center">
        <EmptyState
          variant="error"
          title="Category Not Found"
          description="The category you're looking for doesn't exist or has been moved."
          action={{
            label: 'Browse All Categories',
            onClick: () => window.location.href = '/'
          }}
        />
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const typeFilterOptions = [
    { value: 'all', label: 'All Types', count: allArticles.length },
    { value: 'review', label: 'Reviews', count: allArticles.filter(a => a.type === 'review').length },
    { value: 'guide', label: 'Guides', count: allArticles.filter(a => a.type === 'guide').length },
    { value: 'news', label: 'News', count: allArticles.filter(a => a.type === 'news').length },
    { value: 'comparison', label: 'Comparisons', count: allArticles.filter(a => a.type === 'comparison').length },
  ].filter(option => option.count > 0);

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <SEO
        title={`${category.name} Reviews & Guides - Apex Tech`}
        description={category.description}
        keywords={[category.name.toLowerCase(), 'reviews', 'buying guide', 'tech']}
        ogImage={category.image}
        type="website"
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Category Header */}
        <div className="mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 p-8 lg:p-12"
          >
            {category.image && (
              <div className="absolute inset-0 opacity-10">
                <img
                  src={category.image}
                  alt=""
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            
            <div className="relative z-10">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 tracking-tight">
                {category.name}
              </h1>
              <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl leading-relaxed mb-6">
                {category.description}
              </p>
              <div className="flex flex-wrap items-center gap-6 text-sm text-neutral-500 dark:text-neutral-400">
                <span className="flex items-center">
                  <Star className="w-4 h-4 mr-1" />
                  {category.article_count} articles
                </span>
                <span>Updated weekly</span>
                <span>Expert reviewed</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Filters and Controls */}
        <div className={`transition-all duration-300 ${isFilterSticky ? 'sticky top-16 z-30 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-xl border-b border-neutral-200/50 dark:border-neutral-800/50 -mx-4 px-4 py-4' : ''}`}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-2xl"
          >
            {/* Left side - Filters */}
            <div className="flex flex-wrap items-center gap-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center px-4 py-2 bg-white dark:bg-neutral-700 border border-neutral-200 dark:border-neutral-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-600 transition-colors"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
                <ChevronDown className={`w-4 h-4 ml-2 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
              </button>

              {/* Type Filter Pills */}
              <div className="flex flex-wrap gap-2">
                {typeFilterOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => setTypeFilter(option.value as FilterType)}
                    className={`px-3 py-1.5 text-sm font-medium rounded-full transition-colors ${
                      typeFilter === option.value
                        ? 'bg-primary-600 text-white'
                        : 'bg-white dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-600'
                    }`}
                  >
                    {option.label} ({option.count})
                  </button>
                ))}
              </div>

              {/* Active filters indicator */}
              {typeFilter !== 'all' && (
                <button
                  onClick={() => setTypeFilter('all')}
                  className="flex items-center px-3 py-1.5 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-sm hover:bg-primary-200 dark:hover:bg-primary-900/30 transition-colors"
                >
                  <X className="w-3 h-3 mr-1" />
                  Clear filters
                </button>
              )}
            </div>

            {/* Right side - Sort and View */}
            <div className="flex items-center gap-4">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as SortOption)}
                className="px-4 py-2 bg-white dark:bg-neutral-700 border border-neutral-200 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="rating">Highest Rated</option>
                <option value="title">Alphabetical</option>
              </select>

              <div className="flex items-center bg-white dark:bg-neutral-700 border border-neutral-200 dark:border-neutral-600 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                      : 'text-neutral-500 dark:text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-600'
                  }`}
                  aria-label="Grid view"
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list'
                      ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                      : 'text-neutral-500 dark:text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-600'
                  }`}
                  aria-label="List view"
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </motion.div>

          {/* Results count */}
          <div className="mt-4 text-sm text-neutral-600 dark:text-neutral-400">
            Showing {sortedArticles.length} of {allArticles.length} articles
            {typeFilter !== 'all' && ` in ${typeFilterOptions.find(o => o.value === typeFilter)?.label}`}
          </div>
        </div>

        {/* Articles Grid/List */}
        {sortedArticles.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className={`mt-8 ${
              viewMode === 'grid'
                ? 'grid md:grid-cols-2 lg:grid-cols-3 gap-8'
                : 'space-y-6'
            }`}
          >
            {sortedArticles.map((article, index) => (
              <motion.div
                key={article.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.05 }}
              >
                <AppleCard
                  title={article.title}
                  description={article.excerpt}
                  image={article.featured_image}
                  imageAlt={article.title}
                  href={`/articles/${article.slug}`}
                  badge={{
                    text: article.type,
                    variant: 'primary'
                  }}
                  rating={article.review_data?.rating.overall}
                  author={{
                    name: article.meta.authors[0]?.name,
                    avatar: article.meta.authors[0]?.avatar
                  }}
                  date={formatDate(article.published_date)}
                  readTime={`${article.reading_time} min`}
                  size={viewMode === 'list' ? 'small' : 'medium'}
                  variant={viewMode === 'list' ? 'minimal' : 'default'}
                />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <EmptyState
            variant="articles"
            title={`No ${typeFilter === 'all' ? category.name : typeFilterOptions.find(o => o.value === typeFilter)?.label} Articles Yet`}
            description="Check back soon for new reviews and guides in this category."
            action={{
              label: 'Browse All Categories',
              onClick: () => window.location.href = '/'
            }}
          />
        )}
      </div>
    </div>
  );
}