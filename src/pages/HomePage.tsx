import React from 'react';
import { motion } from 'framer-motion';
import { ArrowR<PERSON>, TrendingUp, Award, Users, Sparkles, Zap, Shield, Star } from 'lucide-react';
import { getFeaturedArticles, getArticlesByType } from '../data/articles';
import { AppleCard } from '../components/ui/AppleCard';
import { NewsletterSignup } from '../components/content/NewsletterSignup';
import { CategoryNavigation } from '../components/layout/CategoryNavigation';
import { FeaturedCategoriesSection } from '../components/sections/FeaturedCategoriesSection';
import { AffiliateProductCard } from '../components/content/AffiliateProductCard';
import { SEO } from '../components/seo/SEO';
import { trackEvent, AnalyticsEvents } from '../lib/analytics';

export function HomePage() {
  const featuredArticles = getFeaturedArticles(3);
  const latestReviews = getArticlesByType('review').slice(0, 6);

  // Mock affiliate products for demonstration
  const featuredProducts = [
    {
      id: 'iphone-15-pro',
      name: 'iPhone 15 Pro',
      brand: 'Apple',
      image: 'https://images.pexels.com/photos/14666018/pexels-photo-14666018.jpeg?auto=compress&cs=tinysrgb&w=400',
      price: { current: 999, msrp: 1099, currency: 'USD' },
      rating: 4.5,
      specs: ['A17 Pro chip', '48MP camera system', 'Titanium design'],
      pros: ['Exceptional camera quality', 'Premium build', 'Great performance'],
      cons: ['Expensive', 'Limited storage options'],
      affiliateLinks: {
        amazon: 'https://amazon.com/iphone-15-pro',
        bestBuy: 'https://bestbuy.com/iphone-15-pro'
      }
    },
    {
      id: 'sony-wh-1000xm5',
      name: 'WH-1000XM5',
      brand: 'Sony',
      image: 'https://images.pexels.com/photos/3945681/pexels-photo-3945681.jpeg?auto=compress&cs=tinysrgb&w=400',
      price: { current: 349, msrp: 399, currency: 'USD' },
      rating: 4.3,
      specs: ['30hr battery', 'Industry-leading ANC', 'Quick charge'],
      pros: ['Excellent noise cancellation', 'Great sound quality', 'Comfortable'],
      cons: ['Not foldable', 'Premium pricing'],
      affiliateLinks: {
        amazon: 'https://amazon.com/sony-wh-1000xm5',
        bestBuy: 'https://bestbuy.com/sony-wh-1000xm5'
      }
    },
    {
      id: 'macbook-pro-m3',
      name: 'MacBook Pro M3',
      brand: 'Apple',
      image: 'https://images.pexels.com/photos/205421/pexels-photo-205421.jpeg?auto=compress&cs=tinysrgb&w=400',
      price: { current: 1599, msrp: 1699, currency: 'USD' },
      rating: 4.7,
      specs: ['M3 chip', '18hr battery', 'Liquid Retina XDR'],
      pros: ['Incredible performance', 'Amazing display', 'Long battery life'],
      cons: ['Very expensive', 'Limited ports'],
      affiliateLinks: {
        amazon: 'https://amazon.com/macbook-pro-m3',
        bestBuy: 'https://bestbuy.com/macbook-pro-m3'
      }
    }
  ];

  const handleArticleClick = (articleId: string, title: string, location: string) => {
    trackEvent(AnalyticsEvents.ARTICLE_VIEW, {
      article_id: articleId,
      article_title: title,
      location,
    });
  };

  const handleCTAClick = (cta: string, location: string) => {
    trackEvent(AnalyticsEvents.CTA_CLICK, {
      cta,
      location,
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <div className="min-h-screen bg-white dark:bg-neutral-900">
      <SEO />

      {/* Hero Section - Inspired by The Verge */}
      <section className="relative overflow-hidden bg-gradient-to-br from-white via-neutral-50/50 to-primary-50/30 dark:from-neutral-900 dark:via-neutral-900 dark:to-primary-950/30">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.05),transparent_50%)] dark:bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]" />
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="mb-8"
            >
              <div className="inline-flex items-center px-4 py-2 bg-primary-100/50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium mb-6 border border-primary-200/50 dark:border-primary-800/50">
                <Sparkles className="w-4 h-4 mr-2" />
                Trusted by 2M+ tech enthusiasts
              </div>
              
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight">
                <span className="bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-600 bg-clip-text text-transparent">
                  Expert Tech Reviews
                </span>
                <br />
                <span className="text-neutral-900 dark:text-neutral-100">
                  You Can Trust
                </span>
              </h1>
              
              <p className="text-xl md:text-2xl text-neutral-600 dark:text-neutral-400 max-w-4xl mx-auto leading-relaxed font-light">
                In-depth analysis, unbiased opinions, and honest recommendations to help you make informed tech decisions with confidence.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              className="flex flex-col sm:flex-row gap-4 justify-center mb-16"
            >
              <a
                href="/categories"
                onClick={() => handleCTAClick('Browse Categories', 'Hero')}
                className="inline-flex items-center px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-2xl transition-all hover:scale-105 shadow-apple hover:shadow-apple-hover focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
              >
                Browse Categories
                <ArrowRight className="ml-2 w-5 h-5" />
              </a>
              
              <a
                href="/about"
                onClick={() => handleCTAClick('Learn More', 'Hero')}
                className="inline-flex items-center px-8 py-4 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 text-neutral-900 dark:text-neutral-100 font-semibold rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50 transition-all hover:scale-105 shadow-apple hover:shadow-apple-hover focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
              >
                Learn More
              </a>
            </motion.div>

            {/* Stats - Apple-style */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
              className="grid grid-cols-3 gap-8 max-w-2xl mx-auto"
            >
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
                  500+
                </div>
                <div className="text-sm text-neutral-600 dark:text-neutral-400 font-medium">
                  Expert Reviews
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
                  2M+
                </div>
                <div className="text-sm text-neutral-600 dark:text-neutral-400 font-medium">
                  Monthly Readers
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-neutral-100 mb-2">
                  5 Years
                </div>
                <div className="text-sm text-neutral-600 dark:text-neutral-400 font-medium">
                  Industry Experience
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Category Navigation */}
      <CategoryNavigation />

      <main id="main-content">
        {/* Featured Article Hero - The Verge style */}
        {featuredArticles.length > 0 && (
          <section className="py-12 lg:py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="relative"
              >
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                  <div className="order-2 lg:order-1">
                    <div className="inline-flex items-center px-3 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-sm font-medium mb-4">
                      <Star className="w-4 h-4 mr-2" />
                      Featured Review
                    </div>
                    
                    <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 tracking-tight leading-tight">
                      {featuredArticles[0].title}
                    </h2>
                    
                    <p className="text-lg text-neutral-600 dark:text-neutral-400 mb-6 leading-relaxed">
                      {featuredArticles[0].excerpt}
                    </p>
                    
                    <div className="flex items-center space-x-6 text-sm text-neutral-500 dark:text-neutral-400 mb-8">
                      <span>By {featuredArticles[0].meta.authors[0]?.name}</span>
                      <span>{formatDate(featuredArticles[0].published_date)}</span>
                      <span>{featuredArticles[0].reading_time} min read</span>
                      {featuredArticles[0].review_data && (
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-warning-400 mr-1" />
                          <span className="font-medium text-neutral-700 dark:text-neutral-300">
                            {featuredArticles[0].review_data.rating.overall.toFixed(1)}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <a
                      href={`/articles/${featuredArticles[0].slug}`}
                      onClick={() => handleArticleClick(featuredArticles[0].id, featuredArticles[0].title, 'Featured Hero')}
                      className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-xl transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
                    >
                      Read Full Review
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </a>
                  </div>
                  
                  <div className="order-1 lg:order-2">
                    <div className="aspect-[4/3] rounded-2xl overflow-hidden shadow-apple dark:shadow-apple-dark">
                      <img
                        src={featuredArticles[0].featured_image}
                        alt={featuredArticles[0].title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </section>
        )}

        {/* Editor's Choice Products */}
        <section className="py-20 lg:py-32 bg-neutral-50/30 dark:bg-neutral-800/10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight">
                Editor's Choice
              </h2>
              <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto leading-relaxed">
                Our top-rated products across all categories, tested and recommended by our expert team.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredProducts.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <AffiliateProductCard
                    product={product}
                    variant="featured"
                  />
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Latest Reviews - The Verge style grid */}
        <section className="py-20 lg:py-32">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between mb-16">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-4 tracking-tight">
                  Latest Reviews
                </h2>
                <p className="text-xl text-neutral-600 dark:text-neutral-400">
                  Fresh insights on the newest tech releases
                </p>
              </motion.div>
              
              <motion.a
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                href="/categories"
                onClick={() => handleCTAClick('View All Reviews', 'Latest Reviews')}
                className="hidden sm:inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
              >
                View All
                <ArrowRight className="ml-2 w-4 h-4" />
              </motion.a>
            </div>

            {/* The Verge-style mixed grid layout */}
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
              {/* Large featured article */}
              {latestReviews[0] && (
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="lg:col-span-8"
                >
                  <AppleCard
                    title={latestReviews[0].title}
                    description={latestReviews[0].excerpt}
                    image={latestReviews[0].featured_image}
                    imageAlt={latestReviews[0].title}
                    href={`/articles/${latestReviews[0].slug}`}
                    onClick={() => handleArticleClick(latestReviews[0].id, latestReviews[0].title, 'Latest Reviews')}
                    badge={{
                      text: latestReviews[0].type,
                      variant: 'primary'
                    }}
                    rating={latestReviews[0].review_data?.rating.overall}
                    author={{
                      name: latestReviews[0].meta.authors[0]?.name,
                      avatar: latestReviews[0].meta.authors[0]?.avatar
                    }}
                    date={formatDate(latestReviews[0].published_date)}
                    readTime={`${latestReviews[0].reading_time} min`}
                    size="large"
                    variant="featured"
                  />
                </motion.div>
              )}

              {/* Sidebar articles */}
              <div className="lg:col-span-4 space-y-6">
                {latestReviews.slice(1, 4).map((article, index) => (
                  <motion.div
                    key={article.id}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: (index + 1) * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <AppleCard
                      title={article.title}
                      description={article.excerpt}
                      image={article.featured_image}
                      imageAlt={article.title}
                      href={`/articles/${article.slug}`}
                      onClick={() => handleArticleClick(article.id, article.title, 'Latest Reviews Sidebar')}
                      badge={{
                        text: article.type,
                        variant: 'primary'
                      }}
                      rating={article.review_data?.rating.overall}
                      date={formatDate(article.published_date)}
                      readTime={`${article.reading_time} min`}
                      size="small"
                      variant="minimal"
                    />
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Bottom row */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
              {latestReviews.slice(4, 7).map((article, index) => (
                <motion.div
                  key={article.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <AppleCard
                    title={article.title}
                    description={article.excerpt}
                    image={article.featured_image}
                    imageAlt={article.title}
                    href={`/articles/${article.slug}`}
                    onClick={() => handleArticleClick(article.id, article.title, 'Latest Reviews Bottom')}
                    badge={{
                      text: article.type,
                      variant: 'primary'
                    }}
                    rating={article.review_data?.rating.overall}
                    author={{
                      name: article.meta.authors[0]?.name,
                      avatar: article.meta.authors[0]?.avatar
                    }}
                    date={formatDate(article.published_date)}
                    readTime={`${article.reading_time} min`}
                    size="medium"
                  />
                </motion.div>
              ))}
            </div>

            <div className="text-center mt-12 sm:hidden">
              <a
                href="/categories"
                onClick={() => handleCTAClick('View All Reviews', 'Latest Reviews Mobile')}
                className="inline-flex items-center px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-all hover:scale-105 shadow-apple focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2"
              >
                View All Reviews
                <ArrowRight className="ml-2 w-4 h-4" />
              </a>
            </div>
          </div>
        </section>

        {/* Featured Categories Section */}
        <FeaturedCategoriesSection />

        {/* Why Trust Us - Apple-style */}
        <section className="py-20 lg:py-32 bg-neutral-50/50 dark:bg-neutral-800/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-20"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 dark:text-neutral-100 mb-6 tracking-tight">
                Why Trust Apex Tech?
              </h2>
              <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto leading-relaxed">
                We're committed to providing honest, thorough, and unbiased tech reviews that help you make confident decisions.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-12">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-apple">
                  <Award className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
                  Expert Analysis
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed text-lg">
                  Our team of experienced reviewers conducts thorough testing and analysis of every product we review, ensuring you get the complete picture.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-success-500 to-success-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-apple">
                  <Shield className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
                  Unbiased Reviews
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed text-lg">
                  We maintain editorial independence and never let sponsorships or partnerships influence our honest opinions and recommendations.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-20 h-20 bg-gradient-to-br from-accent-500 to-accent-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-apple">
                  <Users className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-4">
                  Community Focused
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed text-lg">
                  We listen to our readers and create content that addresses real-world needs, questions, and use cases that matter to you.
                </p>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Newsletter Section - Apple-inspired */}
        <section className="py-20 lg:py-32 bg-gradient-to-br from-primary-50/50 via-white to-secondary-50/30 dark:from-primary-950/20 dark:via-neutral-900 dark:to-secondary-950/20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <NewsletterSignup variant="inline" />
            </motion.div>
          </div>
        </section>
      </main>
    </div>
  );
}