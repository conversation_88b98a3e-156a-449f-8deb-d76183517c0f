import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Enable React Fast Refresh
      fastRefresh: true,
      // Optimize JSX runtime
      jsxRuntime: 'automatic',
    }),
  ],
  optimizeDeps: {
    exclude: ['lucide-react'],
    include: ['react', 'react-dom', 'framer-motion', 'fuse.js'],
  },
  build: {
    // Enable code splitting for better performance
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          animations: ['framer-motion'],
          utils: ['fuse.js', '@tanstack/react-query'],
          ui: ['lucide-react'],
        },
        // Optimize chunk file names
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
      },
    },
    // Enable source maps for debugging in development only
    sourcemap: process.env.NODE_ENV === 'development',
    // Optimize for modern browsers
    target: ['es2020', 'chrome80', 'firefox78', 'safari14'],
    // Minification options
    minify: 'esbuild',
    // Reduce bundle size
    cssCodeSplit: true,
    // Optimize assets
    assetsInlineLimit: 4096,
  },
  server: {
    // Enable HTTP/2 for development
    https: false,
    // Optimize dev server
    hmr: {
      overlay: false,
    },
    // Enable compression
    compress: true,
  },
  // Performance optimizations
  esbuild: {
    // Remove console logs in production
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
    // Enable tree shaking
    treeShaking: true,
  },
  // CSS optimizations
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      // Add any CSS preprocessor options here
    },
  },
  // Preview server optimizations
  preview: {
    port: 4173,
    strictPort: true,
    cors: true,
  },
});