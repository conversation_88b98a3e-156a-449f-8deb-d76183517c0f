<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Apex Tech - The definitive source for in-depth tech reviews, comparisons, and buying guides. Unbiased analysis of the latest gadgets and gear." />
    <meta name="theme-color" content="#ffffff" />
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://images.pexels.com">
    
    <!-- Optimized font loading -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <title>Apex Tech - Expert Tech Reviews & Analysis</title>
    <link rel="manifest" href="/manifest.webmanifest" />
    
    <!-- Prevent theme flicker - Default to light mode -->
    <script>
      (function() {
        const theme = localStorage.getItem('apex-tech-theme') || 'light';
        if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          document.documentElement.classList.add('dark');
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0f172a');
        } else {
          document.documentElement.classList.remove('dark');
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#ffffff');
        }
      })();
    </script>
    
    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="//images.pexels.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    
    <!-- Critical CSS inlined for faster rendering -->
    <style>
      /* Critical above-the-fold styles */
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Inter', 'SF Pro Display', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        margin: 0;
        padding: 0;
        background-color: #ffffff;
        color: #171717;
        transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      
      html.dark body {
        background-color: #0a0a0a;
        color: #fafafa;
      }
      
      /* Loading spinner for initial page load */
      .loading-spinner {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        border: 3px solid #e5e5e5;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 9999;
      }
      
      @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
      }
      
      /* Hide loading spinner when React loads */
      #root:not(:empty) + .loading-spinner {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <div class="loading-spinner"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Analytics placeholder - replace with actual analytics -->
    <script>
      // Placeholder for analytics initialization
      // Replace with actual analytics code (GA4, Plausible, etc.)
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      // gtag('config', 'GA_MEASUREMENT_ID');
    </script>
  </body>
</html>